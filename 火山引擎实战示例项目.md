# 火山引擎云控下发和实验下发实战示例

## 项目概述

本示例展示如何在一个Android电商应用中集成火山引擎的云控下发和A/B测试功能，包括：
- 商品页面UI优化实验
- 购买流程优化
- 个性化推荐算法测试
- 动态配置管理

## Android项目结构

```
ECommerceApp/
├── app/
│   ├── src/main/java/com/example/ecommerce/
│   │   ├── activities/
│   │   │   ├── MainActivity.java
│   │   │   ├── ProductDetailActivity.java
│   │   │   └── CheckoutActivity.java
│   │   ├── fragments/
│   │   │   ├── ProductListFragment.java
│   │   │   └── RecommendationFragment.java
│   │   ├── adapters/
│   │   │   ├── ProductAdapter.java
│   │   │   └── RecommendationAdapter.java
│   │   ├── services/
│   │   │   ├── DataTesterService.java
│   │   │   ├── ProductService.java
│   │   │   └── UserService.java
│   │   ├── utils/
│   │   │   ├── ExperimentTracker.java
│   │   │   └── ConfigManager.java
│   │   ├── models/
│   │   │   ├── Product.java
│   │   │   ├── User.java
│   │   │   └── ExperimentConfig.java
│   │   └── MyApplication.java
│   ├── src/main/res/
│   │   ├── layout/
│   │   │   ├── activity_main.xml
│   │   │   ├── activity_main_new.xml (实验版本)
│   │   │   ├── item_product_card.xml
│   │   │   └── item_product_card_new.xml (实验版本)
│   │   ├── values/
│   │   │   ├── strings.xml
│   │   │   ├── colors.xml
│   │   │   └── datatester_config.xml
│   │   └── drawable/
│   └── build.gradle
├── gradle.properties
└── build.gradle
```

## 1. Android初始化配置

### app/build.gradle
```gradle
android {
    compileSdk 34

    defaultConfig {
        applicationId "com.example.ecommerce"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        // DataTester配置
        buildConfigField "String", "DATATESTER_APP_KEY", "\"${project.findProperty('DATATESTER_APP_KEY') ?: 'your_app_key'}\""
        buildConfigField "boolean", "DATATESTER_DEBUG", "${project.findProperty('DATATESTER_DEBUG') ?: 'false'}"
    }

    buildTypes {
        debug {
            buildConfigField "String", "DATATESTER_CHANNEL", "\"debug\""
            buildConfigField "boolean", "DATATESTER_DEBUG", "true"
        }
        release {
            buildConfigField "String", "DATATESTER_CHANNEL", "\"release\""
            buildConfigField "boolean", "DATATESTER_DEBUG", "false"
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation 'com.volcengine:datatester-android-sdk:2.1.5'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.recyclerview:recyclerview:1.3.1'

    // 网络请求
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.10.0'

    // 图片加载
    implementation 'com.github.bumptech.glide:glide:4.15.1'
}
```

### src/main/res/values/datatester_config.xml
```xml
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 实验配置键名 -->
    <string name="experiment_product_card_design">product_card_design_test</string>
    <string name="experiment_checkout_flow">checkout_flow_optimization</string>
    <string name="experiment_recommendation_algorithm">recommendation_algo_test</string>
    <string name="experiment_pricing_strategy">dynamic_pricing_test</string>

    <!-- Feature Flag配置键名 -->
    <string name="feature_new_ui_theme">new_ui_theme</string>
    <string name="feature_advanced_search">advanced_search_enabled</string>
    <string name="feature_live_chat">live_chat_support</string>
    <string name="feature_personalized_homepage">personalized_homepage</string>

    <!-- 默认配置值 -->
    <string name="default_theme_color">#1890ff</string>
    <integer name="default_max_retry_count">3</integer>
    <integer name="default_items_per_page">10</integer>
</resources>
```

### utils/DataTesterConstants.java
```java
package com.example.ecommerce.utils;

public class DataTesterConstants {

    // 实验键名
    public static final class Experiments {
        public static final String PRODUCT_CARD_DESIGN = "product_card_design_test";
        public static final String CHECKOUT_FLOW = "checkout_flow_optimization";
        public static final String RECOMMENDATION_ALGORITHM = "recommendation_algo_test";
        public static final String PRICING_STRATEGY = "dynamic_pricing_test";
    }

    // Feature Flag键名
    public static final class Features {
        public static final String NEW_UI_THEME = "new_ui_theme";
        public static final String ADVANCED_SEARCH = "advanced_search_enabled";
        public static final String LIVE_CHAT = "live_chat_support";
        public static final String PERSONALIZED_HOMEPAGE = "personalized_homepage";
    }

    // 事件名称
    public static final class Events {
        public static final String PRODUCT_VIEW = "product_view";
        public static final String ADD_TO_CART = "add_to_cart";
        public static final String PURCHASE_COMPLETED = "purchase_completed";
        public static final String BUTTON_CLICK = "button_click";
        public static final String EXPERIMENT_EXPOSURE = "experiment_exposure";
    }

    // 用户属性键名
    public static final class UserProperties {
        public static final String USER_LEVEL = "user_level";
        public static final String REGISTRATION_DATE = "registration_date";
        public static final String CITY = "city";
        public static final String AGE = "age";
        public static final String GENDER = "gender";
        public static final String DEVICE_TYPE = "device_type";
        public static final String APP_VERSION = "app_version";
    }
}

### MyApplication.java
```java
package com.example.ecommerce;

import android.app.Application;
import android.util.Log;
import com.volcengine.datatester.DataTester;
import com.volcengine.datatester.DataTesterConfig;
import com.volcengine.datatester.InitCallback;
import com.volcengine.datatester.LogLevel;
import com.example.ecommerce.services.DataTesterService;

public class MyApplication extends Application {
    private static final String TAG = "MyApplication";

    @Override
    public void onCreate() {
        super.onCreate();
        initDataTester();
    }

    private void initDataTester() {
        DataTesterConfig config = new DataTesterConfig.Builder()
            .setAppKey(BuildConfig.DATATESTER_APP_KEY)
            .setChannel(BuildConfig.DATATESTER_CHANNEL)
            .setDebugMode(BuildConfig.DATATESTER_DEBUG)
            .setLogLevel(BuildConfig.DATATESTER_DEBUG ? LogLevel.DEBUG : LogLevel.ERROR)
            .setNetworkTimeout(10000)
            .setCacheEnabled(true)
            .setAutoTrackEnabled(true)
            .build();

        DataTester.init(this, config, new InitCallback() {
            @Override
            public void onSuccess() {
                Log.d(TAG, "DataTester SDK 初始化成功");
                DataTesterService.getInstance().setInitialized(true);
            }

            @Override
            public void onFailure(String error) {
                Log.e(TAG, "DataTester SDK 初始化失败: " + error);
                DataTesterService.getInstance().setInitialized(false);
            }
        });
    }
}
```

### services/DataTesterService.java
```java
package com.example.ecommerce.services;

import android.util.Log;
import com.volcengine.datatester.DataTester;
import com.volcengine.datatester.ExperimentConfig;
import com.volcengine.datatester.FeatureFlagCallback;
import com.volcengine.datatester.ExperimentCallback;
import com.example.ecommerce.utils.DataTesterConstants;
import java.util.HashMap;
import java.util.Map;

public class DataTesterService {
    private static final String TAG = "DataTesterService";
    private static DataTesterService instance;

    private boolean isInitialized = false;
    private String currentUserId;
    private Map<String, Object> userAttributes;

    private DataTesterService() {
        this.userAttributes = new HashMap<>();
    }

    public static synchronized DataTesterService getInstance() {
        if (instance == null) {
            instance = new DataTesterService();
        }
        return instance;
    }

    public void setInitialized(boolean initialized) {
        this.isInitialized = initialized;
    }

    public boolean isInitialized() {
        return isInitialized;
    }

    public void setUser(String userId, Map<String, Object> attributes) {
        this.currentUserId = userId;
        this.userAttributes = attributes != null ? new HashMap<>(attributes) : new HashMap<>();

        if (isInitialized) {
            DataTester.setUserId(userId);
            DataTester.setUserProperties(this.userAttributes);
        }
    }

    public String getCurrentUserId() {
        return currentUserId;
    }

    // 获取Feature Flag
    public <T> T getFeatureFlag(String flagKey, T defaultValue) {
        if (!isInitialized || currentUserId == null) {
            Log.w(TAG, "SDK未初始化或用户ID为空，返回默认值");
            return defaultValue;
        }

        try {
            return DataTester.getFeatureFlag(flagKey, defaultValue, currentUserId, userAttributes);
        } catch (Exception e) {
            Log.e(TAG, "获取Feature Flag失败: " + flagKey, e);
            return defaultValue;
        }
    }

    // 异步获取Feature Flag
    public <T> void getFeatureFlagAsync(String flagKey, T defaultValue, FeatureFlagCallback<T> callback) {
        if (!isInitialized || currentUserId == null) {
            Log.w(TAG, "SDK未初始化或用户ID为空");
            callback.onFailure("SDK未初始化或用户ID为空");
            return;
        }

        DataTester.getFeatureFlagAsync(flagKey, defaultValue, currentUserId, callback);
    }

    // 获取实验配置
    public ExperimentConfig getExperimentConfig(String experimentKey) {
        if (!isInitialized || currentUserId == null) {
            Log.w(TAG, "SDK未初始化或用户ID为空，返回null");
            return null;
        }

        try {
            return DataTester.getExperimentConfig(experimentKey, currentUserId);
        } catch (Exception e) {
            Log.e(TAG, "获取实验配置失败: " + experimentKey, e);
            return null;
        }
    }

    // 异步获取实验配置
    public void getExperimentConfigAsync(String experimentKey, ExperimentCallback<ExperimentConfig> callback) {
        if (!isInitialized || currentUserId == null) {
            Log.w(TAG, "SDK未初始化或用户ID为空");
            callback.onFailure("SDK未初始化或用户ID为空");
            return;
        }

        DataTester.getExperimentConfigAsync(experimentKey, currentUserId, callback);
    }

    // 追踪事件
    public void trackEvent(String eventName, Map<String, Object> properties) {
        if (!isInitialized) {
            Log.w(TAG, "SDK未初始化，无法追踪事件: " + eventName);
            return;
        }

        try {
            Map<String, Object> eventProps = new HashMap<>();
            if (properties != null) {
                eventProps.putAll(properties);
            }
            eventProps.put("user_id", currentUserId);
            eventProps.put("timestamp", System.currentTimeMillis());

            DataTester.trackEvent(eventName, eventProps);
        } catch (Exception e) {
            Log.e(TAG, "事件追踪失败: " + eventName, e);
        }
    }

    // 追踪实验曝光
    public void trackExposure(String experimentKey, Map<String, Object> properties) {
        if (!isInitialized || currentUserId == null) {
            Log.w(TAG, "SDK未初始化或用户ID为空，无法追踪曝光: " + experimentKey);
            return;
        }

        try {
            DataTester.trackExposure(experimentKey, currentUserId, properties);
        } catch (Exception e) {
            Log.e(TAG, "曝光追踪失败: " + experimentKey, e);
        }
    }

    // 便捷方法：获取常用Feature Flag
    public boolean isNewUIThemeEnabled() {
        return getFeatureFlag(DataTesterConstants.Features.NEW_UI_THEME, false);
    }

    public boolean isAdvancedSearchEnabled() {
        return getFeatureFlag(DataTesterConstants.Features.ADVANCED_SEARCH, false);
    }

    public boolean isLiveChatEnabled() {
        return getFeatureFlag(DataTesterConstants.Features.LIVE_CHAT, false);
    }

    public boolean isPersonalizedHomepageEnabled() {
        return getFeatureFlag(DataTesterConstants.Features.PERSONALIZED_HOMEPAGE, false);
    }

    // 便捷方法：追踪常用事件
    public void trackProductView(String productId, String category) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("product_id", productId);
        properties.put("category", category);
        trackEvent(DataTesterConstants.Events.PRODUCT_VIEW, properties);
    }

    public void trackAddToCart(String productId, double price, int quantity) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("product_id", productId);
        properties.put("price", price);
        properties.put("quantity", quantity);
        trackEvent(DataTesterConstants.Events.ADD_TO_CART, properties);
    }

    public void trackPurchaseCompleted(String orderId, double amount, String currency) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("order_id", orderId);
        properties.put("amount", amount);
        properties.put("currency", currency);
        trackEvent(DataTesterConstants.Events.PURCHASE_COMPLETED, properties);
    }
}

  setUser(userId, attributes = {}) {
    this.currentUserId = userId;
    this.userAttributes = attributes;
    
    if (this.isInitialized) {
      DataTester.setUserId(userId);
      DataTester.setUserProperty(userId, attributes);
    }
  }

  // 获取Feature Flag
  getFeatureFlag(flagKey, defaultValue) {
    if (!this.isInitialized || !this.currentUserId) {
      return defaultValue;
    }

    try {
      return DataTester.getFeatureFlag(
        flagKey,
        defaultValue,
        this.currentUserId,
        this.userAttributes
      );
    } catch (error) {
      console.error(`获取Feature Flag失败: ${flagKey}`, error);
      return defaultValue;
    }
  }

  // 获取实验配置
  getExperimentConfig(experimentKey, defaultConfig) {
    if (!this.isInitialized || !this.currentUserId) {
      return { ...defaultConfig, variant_name: 'control' };
    }

    try {
      return DataTester.getExperimentConfig(
        experimentKey,
        defaultConfig,
        this.currentUserId,
        this.userAttributes
      );
    } catch (error) {
      console.error(`获取实验配置失败: ${experimentKey}`, error);
      return { ...defaultConfig, variant_name: 'control' };
    }
  }

  // 追踪事件
  trackEvent(eventName, properties = {}) {
    if (!this.isInitialized) return;

    try {
      DataTester.trackEvent(eventName, {
        ...properties,
        user_id: this.currentUserId,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error(`事件追踪失败: ${eventName}`, error);
    }
  }

  // 追踪实验曝光
  trackExposure(experimentKey, properties = {}) {
    if (!this.isInitialized || !this.currentUserId) return;

    try {
      DataTester.trackExposure(experimentKey, this.currentUserId, properties);
    } catch (error) {
      console.error(`曝光追踪失败: ${experimentKey}`, error);
    }
  }

  handleConfigUpdate(updatedConfigs) {
    console.log('配置已更新:', updatedConfigs);
    // 触发应用重新渲染或配置更新
    window.dispatchEvent(new CustomEvent('datatester-config-updated', {
      detail: updatedConfigs
    }));
  }
}

export default new DataTesterService();
```

## 2. Android商品卡片设计实验

### adapters/ProductAdapter.java
```java
package com.example.ecommerce.adapters;

import android.content.Context;
import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.bumptech.glide.Glide;
import com.volcengine.datatester.ExperimentConfig;
import com.example.ecommerce.R;
import com.example.ecommerce.models.Product;
import com.example.ecommerce.services.DataTesterService;
import com.example.ecommerce.utils.DataTesterConstants;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProductAdapter extends RecyclerView.Adapter<ProductAdapter.ProductViewHolder> {
    private static final String TAG = "ProductAdapter";

    private Context context;
    private List<Product> products;
    private OnProductClickListener listener;
    private DataTesterService dataTesterService;

    public interface OnProductClickListener {
        void onAddToCart(Product product);
        void onProductClick(Product product);
    }

    public ProductAdapter(Context context, List<Product> products, OnProductClickListener listener) {
        this.context = context;
        this.products = products;
        this.listener = listener;
        this.dataTesterService = DataTesterService.getInstance();
    }

    @NonNull
    @Override
    public ProductViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        // 根据实验配置选择布局
        int layoutId = getLayoutForExperiment();
        View view = LayoutInflater.from(context).inflate(layoutId, parent, false);
        return new ProductViewHolder(view);
    }

    private int getLayoutForExperiment() {
        ExperimentConfig config = dataTesterService.getExperimentConfig(
            DataTesterConstants.Experiments.PRODUCT_CARD_DESIGN
        );

        if (config != null) {
            Map<String, Object> variables = config.getVariables();
            String layout = (String) variables.getOrDefault("layout", "vertical");

            if ("horizontal".equals(layout)) {
                return R.layout.item_product_card_horizontal;
            }
        }

        return R.layout.item_product_card_vertical; // 默认布局
    }

    @Override
    public void onBindViewHolder(@NonNull ProductViewHolder holder, int position) {
        Product product = products.get(position);

        // 获取实验配置
        ExperimentConfig experimentConfig = dataTesterService.getExperimentConfig(
            DataTesterConstants.Experiments.PRODUCT_CARD_DESIGN
        );

        // 应用实验配置
        applyExperimentConfig(holder, product, experimentConfig);

        // 追踪商品曝光
        trackProductExposure(product, experimentConfig);

        // 设置点击事件
        setupClickListeners(holder, product, experimentConfig);
    }

    private void applyExperimentConfig(ProductViewHolder holder, Product product, ExperimentConfig config) {
        // 设置基础信息
        holder.productName.setText(product.getName());
        holder.productPrice.setText(String.format("¥%.2f", product.getPrice()));

        // 加载商品图片
        Glide.with(context)
            .load(product.getImageUrl())
            .placeholder(R.drawable.placeholder_product)
            .into(holder.productImage);

        // 应用实验配置
        if (config != null) {
            Map<String, Object> variables = config.getVariables();
            String variantName = config.getVariantName();

            // 显示评分配置
            boolean showRating = (Boolean) variables.getOrDefault("show_rating", true);
            if (showRating && holder.ratingLayout != null) {
                holder.ratingLayout.setVisibility(View.VISIBLE);
                holder.ratingText.setText(String.format("⭐ %.1f (%d评价)",
                    product.getRating(), product.getReviewCount()));
            } else if (holder.ratingLayout != null) {
                holder.ratingLayout.setVisibility(View.GONE);
            }

            // 显示折扣配置
            boolean showDiscount = (Boolean) variables.getOrDefault("show_discount", true);
            if (showDiscount && product.getOriginalPrice() > product.getPrice() && holder.originalPrice != null) {
                holder.originalPrice.setVisibility(View.VISIBLE);
                holder.originalPrice.setText(String.format("¥%.2f", product.getOriginalPrice()));
            } else if (holder.originalPrice != null) {
                holder.originalPrice.setVisibility(View.GONE);
            }

            // 按钮样式配置
            String buttonStyle = (String) variables.getOrDefault("button_style", "primary");
            String buttonText = (String) variables.getOrDefault("button_text", "加入购物车");

            holder.addToCartButton.setText(buttonText);
            applyButtonStyle(holder.addToCartButton, buttonStyle);

            Log.d(TAG, "应用实验配置 - 分组: " + variantName + ", 按钮样式: " + buttonStyle);
        }
    }

    private void applyButtonStyle(Button button, String style) {
        switch (style) {
            case "secondary":
                button.setBackgroundColor(Color.parseColor("#f0f0f0"));
                button.setTextColor(Color.parseColor("#333333"));
                break;
            case "success":
                button.setBackgroundColor(Color.parseColor("#52c41a"));
                button.setTextColor(Color.WHITE);
                break;
            case "warning":
                button.setBackgroundColor(Color.parseColor("#faad14"));
                button.setTextColor(Color.WHITE);
                break;
            default: // primary
                button.setBackgroundColor(Color.parseColor("#1890ff"));
                button.setTextColor(Color.WHITE);
                break;
        }
    }

    private void trackProductExposure(Product product, ExperimentConfig config) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("product_id", product.getId());
        properties.put("category", product.getCategory());
        properties.put("page", "product_list");
        properties.put("position", products.indexOf(product));

        if (config != null) {
            properties.put("variant", config.getVariantName());
        }

        dataTesterService.trackExposure(
            DataTesterConstants.Experiments.PRODUCT_CARD_DESIGN,
            properties
        );
    }

    private void setupClickListeners(ProductViewHolder holder, Product product, ExperimentConfig config) {
        // 商品点击
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onProductClick(product);
            }

            // 追踪商品点击
            dataTesterService.trackProductView(product.getId(), product.getCategory());
        });

        // 加入购物车按钮点击
        holder.addToCartButton.setOnClickListener(v -> {
            if (listener != null) {
                listener.onAddToCart(product);
            }

            // 追踪加入购物车事件
            Map<String, Object> properties = new HashMap<>();
            properties.put("product_id", product.getId());
            properties.put("experiment", DataTesterConstants.Experiments.PRODUCT_CARD_DESIGN);
            properties.put("price", product.getPrice());

            if (config != null) {
                properties.put("variant", config.getVariantName());
                Map<String, Object> variables = config.getVariables();
                properties.put("button_style", variables.getOrDefault("button_style", "primary"));
            }

            dataTesterService.trackEvent(DataTesterConstants.Events.ADD_TO_CART, properties);
        });
    }

    @Override
    public int getItemCount() {
        return products != null ? products.size() : 0;
    }

    public static class ProductViewHolder extends RecyclerView.ViewHolder {
        ImageView productImage;
        TextView productName;
        TextView productPrice;
        TextView originalPrice;
        TextView ratingText;
        View ratingLayout;
        Button addToCartButton;

        public ProductViewHolder(@NonNull View itemView) {
            super(itemView);
            productImage = itemView.findViewById(R.id.iv_product_image);
            productName = itemView.findViewById(R.id.tv_product_name);
            productPrice = itemView.findViewById(R.id.tv_product_price);
            originalPrice = itemView.findViewById(R.id.tv_original_price);
            ratingText = itemView.findViewById(R.id.tv_rating);
            ratingLayout = itemView.findViewById(R.id.layout_rating);
            addToCartButton = itemView.findViewById(R.id.btn_add_to_cart);
        }
    }
}
```

### res/layout/item_product_card_vertical.xml
```xml
<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <ImageView
            android:id="@+id/iv_product_image"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:scaleType="centerCrop"
            android:background="@color/gray_light" />

        <TextView
            android:id="@+id/tv_product_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textSize="16sp"
            android:textStyle="bold"
            android:maxLines="2"
            android:ellipsize="end" />

        <LinearLayout
            android:id="@+id/layout_rating"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_rating"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@color/gray_dark" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_product_price"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/price_color" />

            <TextView
                android:id="@+id/tv_original_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textSize="14sp"
                android:textColor="@color/gray_dark"
                android:background="@drawable/strikethrough" />

        </LinearLayout>

        <Button
            android:id="@+id/btn_add_to_cart"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="加入购物车"
            android:textColor="@android:color/white"
            android:background="@drawable/button_primary" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
```

## 3. 购买流程优化实验

### components/CheckoutFlow.js
```javascript
import React, { useEffect, useState } from 'react';
import DataTesterService from '../services/DataTesterService';
import { DataTesterConfig } from '../config/datatester.config';

const CheckoutFlow = ({ cartItems, onComplete }) => {
  const [flowConfig, setFlowConfig] = useState({
    steps: 3,
    layout: 'vertical',
    showProgress: true,
    guestCheckout: false,
    variant_name: 'control'
  });

  const [currentStep, setCurrentStep] = useState(1);

  useEffect(() => {
    // 获取购买流程实验配置
    const config = DataTesterService.getExperimentConfig(
      DataTesterConfig.experiments.CHECKOUT_FLOW,
      {
        steps: 3,
        layout: 'vertical',
        showProgress: true,
        guestCheckout: false
      }
    );
    
    setFlowConfig(config);

    // 追踪购买流程开始
    DataTesterService.trackEvent('checkout_started', {
      experiment: DataTesterConfig.experiments.CHECKOUT_FLOW,
      variant: config.variant_name,
      cart_value: cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0),
      item_count: cartItems.length
    });

    // 追踪曝光
    DataTesterService.trackExposure(
      DataTesterConfig.experiments.CHECKOUT_FLOW,
      {
        page: 'checkout',
        cart_value: cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0)
      }
    );
  }, [cartItems]);

  const handleStepComplete = (step) => {
    DataTesterService.trackEvent('checkout_step_completed', {
      step: step,
      experiment: DataTesterConfig.experiments.CHECKOUT_FLOW,
      variant: flowConfig.variant_name
    });

    if (step < flowConfig.steps) {
      setCurrentStep(step + 1);
    } else {
      handleCheckoutComplete();
    }
  };

  const handleCheckoutComplete = () => {
    const totalValue = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
    
    // 追踪转化事件
    DataTesterService.trackEvent('purchase_completed', {
      experiment: DataTesterConfig.experiments.CHECKOUT_FLOW,
      variant: flowConfig.variant_name,
      order_value: totalValue,
      item_count: cartItems.length
    });

    onComplete();
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <ShippingInfo onComplete={() => handleStepComplete(1)} />;
      case 2:
        if (flowConfig.steps === 2) {
          return <PaymentInfo onComplete={() => handleStepComplete(2)} />;
        }
        return <PaymentMethod onComplete={() => handleStepComplete(2)} />;
      case 3:
        return <OrderReview onComplete={() => handleStepComplete(3)} />;
      default:
        return null;
    }
  };

  return (
    <div className={`checkout-flow ${flowConfig.layout} ${flowConfig.variant_name}`}>
      {flowConfig.showProgress && (
        <div className="progress-bar">
          {Array.from({ length: flowConfig.steps }, (_, i) => (
            <div 
              key={i} 
              className={`step ${i + 1 <= currentStep ? 'active' : ''}`}
            >
              {i + 1}
            </div>
          ))}
        </div>
      )}

      <div className="step-content">
        {renderStep()}
      </div>

      {flowConfig.guestCheckout && currentStep === 1 && (
        <div className="guest-checkout-option">
          <button onClick={() => handleStepComplete(1)}>
            游客结账
          </button>
        </div>
      )}
    </div>
  );
};

// 子组件示例
const ShippingInfo = ({ onComplete }) => (
  <div className="shipping-info">
    <h3>配送信息</h3>
    {/* 配送表单 */}
    <button onClick={onComplete}>下一步</button>
  </div>
);

const PaymentMethod = ({ onComplete }) => (
  <div className="payment-method">
    <h3>支付方式</h3>
    {/* 支付方式选择 */}
    <button onClick={onComplete}>下一步</button>
  </div>
);

const PaymentInfo = ({ onComplete }) => (
  <div className="payment-info">
    <h3>支付信息</h3>
    {/* 支付信息表单 */}
    <button onClick={onComplete}>完成支付</button>
  </div>
);

const OrderReview = ({ onComplete }) => (
  <div className="order-review">
    <h3>订单确认</h3>
    {/* 订单详情 */}
    <button onClick={onComplete}>确认订单</button>
  </div>
);

export default CheckoutFlow;
```

## 4. 推荐算法实验

### components/RecommendationList.js
```javascript
import React, { useEffect, useState } from 'react';
import DataTesterService from '../services/DataTesterService';
import ProductService from '../services/ProductService';
import { DataTesterConfig } from '../config/datatester.config';

const RecommendationList = ({ userId, currentProductId }) => {
  const [recommendations, setRecommendations] = useState([]);
  const [algorithmConfig, setAlgorithmConfig] = useState({
    algorithm: 'collaborative_filtering',
    count: 6,
    include_popular: true,
    variant_name: 'control'
  });

  useEffect(() => {
    loadRecommendations();
  }, [userId, currentProductId]);

  const loadRecommendations = async () => {
    // 获取推荐算法实验配置
    const config = DataTesterService.getExperimentConfig(
      DataTesterConfig.experiments.RECOMMENDATION_ALGORITHM,
      {
        algorithm: 'collaborative_filtering',
        count: 6,
        include_popular: true
      }
    );
    
    setAlgorithmConfig(config);

    try {
      // 根据实验配置获取推荐结果
      const recs = await ProductService.getRecommendations(
        userId,
        currentProductId,
        config
      );
      
      setRecommendations(recs);

      // 追踪推荐曝光
      DataTesterService.trackExposure(
        DataTesterConfig.experiments.RECOMMENDATION_ALGORITHM,
        {
          algorithm: config.algorithm,
          recommendation_count: recs.length,
          current_product_id: currentProductId
        }
      );

      // 追踪推荐展示事件
      DataTesterService.trackEvent('recommendations_shown', {
        experiment: DataTesterConfig.experiments.RECOMMENDATION_ALGORITHM,
        variant: config.variant_name,
        algorithm: config.algorithm,
        product_ids: recs.map(r => r.id),
        count: recs.length
      });

    } catch (error) {
      console.error('加载推荐失败:', error);
    }
  };

  const handleRecommendationClick = (product, index) => {
    // 追踪推荐点击
    DataTesterService.trackEvent('recommendation_click', {
      experiment: DataTesterConfig.experiments.RECOMMENDATION_ALGORITHM,
      variant: algorithmConfig.variant_name,
      algorithm: algorithmConfig.algorithm,
      product_id: product.id,
      position: index,
      current_product_id: currentProductId
    });
  };

  return (
    <div className="recommendation-list">
      <h3>为您推荐</h3>
      <div className="recommendations">
        {recommendations.map((product, index) => (
          <div 
            key={product.id} 
            className="recommendation-item"
            onClick={() => handleRecommendationClick(product, index)}
          >
            <img src={product.image} alt={product.name} />
            <div className="product-info">
              <h4>{product.name}</h4>
              <span className="price">¥{product.price}</span>
              {algorithmConfig.algorithm === 'content_based' && (
                <span className="similarity">相似度: {product.similarity}%</span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecommendationList;
```

## 5. 动态配置管理

### utils/ConfigManager.js
```javascript
import DataTesterService from '../services/DataTesterService';
import { DataTesterConfig } from '../config/datatester.config';

class ConfigManager {
  constructor() {
    this.configs = {};
    this.listeners = [];
    
    // 监听配置更新
    window.addEventListener('datatester-config-updated', this.handleConfigUpdate.bind(this));
  }

  // 获取UI主题配置
  getUITheme() {
    const isNewTheme = DataTesterService.getFeatureFlag(
      DataTesterConfig.features.NEW_UI_THEME,
      false
    );

    return {
      theme: isNewTheme ? 'modern' : 'classic',
      primaryColor: isNewTheme ? '#6366f1' : '#1890ff',
      borderRadius: isNewTheme ? '12px' : '6px',
      fontFamily: isNewTheme ? 'Inter, sans-serif' : 'Arial, sans-serif'
    };
  }

  // 获取搜索功能配置
  getSearchConfig() {
    const advancedSearchEnabled = DataTesterService.getFeatureFlag(
      DataTesterConfig.features.ADVANCED_SEARCH,
      false
    );

    return {
      enableAdvancedSearch: advancedSearchEnabled,
      enableAutoComplete: true,
      enableSearchHistory: advancedSearchEnabled,
      maxSuggestions: advancedSearchEnabled ? 10 : 5
    };
  }

  // 获取客服配置
  getCustomerServiceConfig() {
    const liveChatEnabled = DataTesterService.getFeatureFlag(
      DataTesterConfig.features.LIVE_CHAT,
      false
    );

    return {
      enableLiveChat: liveChatEnabled,
      enableChatBot: true,
      workingHours: liveChatEnabled ? '9:00-21:00' : '9:00-18:00',
      maxWaitTime: liveChatEnabled ? 30 : 60 // 秒
    };
  }

  // 获取个性化首页配置
  getHomepageConfig() {
    const personalizedEnabled = DataTesterService.getFeatureFlag(
      DataTesterConfig.features.PERSONALIZED_HOMEPAGE,
      false
    );

    return {
      enablePersonalization: personalizedEnabled,
      showRecommendations: personalizedEnabled,
      showRecentlyViewed: true,
      maxRecommendations: personalizedEnabled ? 12 : 6
    };
  }

  // 获取所有配置
  getAllConfigs() {
    return {
      ui: this.getUITheme(),
      search: this.getSearchConfig(),
      customerService: this.getCustomerServiceConfig(),
      homepage: this.getHomepageConfig()
    };
  }

  // 添加配置变更监听器
  addConfigListener(callback) {
    this.listeners.push(callback);
  }

  // 移除配置变更监听器
  removeConfigListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  // 处理配置更新
  handleConfigUpdate(event) {
    const updatedConfigs = this.getAllConfigs();
    this.configs = updatedConfigs;
    
    // 通知所有监听器
    this.listeners.forEach(callback => {
      try {
        callback(updatedConfigs);
      } catch (error) {
        console.error('配置监听器执行失败:', error);
      }
    });
  }
}

export default new ConfigManager();
```

## 6. Android应用入口集成

### MainActivity.java
```java
package com.example.ecommerce.activities;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import com.example.ecommerce.R;
import com.example.ecommerce.fragments.ProductListFragment;
import com.example.ecommerce.fragments.RecommendationFragment;
import com.example.ecommerce.models.User;
import com.example.ecommerce.services.DataTesterService;
import com.example.ecommerce.services.UserService;
import com.example.ecommerce.utils.ConfigManager;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import java.util.HashMap;
import java.util.Map;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";

    private DataTesterService dataTesterService;
    private ConfigManager configManager;
    private User currentUser;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 初始化服务
        initializeServices();

        // 设置用户信息
        setupUser();

        // 应用配置
        applyConfigurations();

        // 设置UI
        setupUI();

        // 追踪应用启动
        trackAppLaunch();
    }

    private void initializeServices() {
        dataTesterService = DataTesterService.getInstance();
        configManager = ConfigManager.getInstance();

        if (!dataTesterService.isInitialized()) {
            Log.w(TAG, "DataTester SDK未初始化，某些功能可能不可用");
        }
    }

    private void setupUser() {
        // 获取当前用户信息
        currentUser = UserService.getInstance().getCurrentUser();

        if (currentUser != null) {
            // 设置用户属性
            Map<String, Object> userAttributes = new HashMap<>();
            userAttributes.put("user_level", currentUser.getUserLevel());
            userAttributes.put("registration_date", currentUser.getRegistrationDate());
            userAttributes.put("city", currentUser.getCity());
            userAttributes.put("age", currentUser.getAge());
            userAttributes.put("gender", currentUser.getGender());

            dataTesterService.setUser(currentUser.getId(), userAttributes);
            Log.d(TAG, "用户信息设置完成: " + currentUser.getId());
        }
    }

    private void applyConfigurations() {
        // 获取UI配置
        ConfigManager.UIConfig uiConfig = configManager.getUIConfig();

        // 应用主题
        if ("dark".equals(uiConfig.getTheme())) {
            setTheme(R.style.DarkTheme);
        } else {
            setTheme(R.style.LightTheme);
        }

        // 设置内容视图
        setContentView(R.layout.activity_main);

        // 应用主题色
        View toolbar = findViewById(R.id.toolbar);
        if (toolbar != null) {
            toolbar.setBackgroundColor(uiConfig.getPrimaryColor());
        }
    }

    private void setupUI() {
        // 设置底部导航
        BottomNavigationView bottomNav = findViewById(R.id.bottom_navigation);
        bottomNav.setOnItemSelectedListener(item -> {
            Fragment selectedFragment = null;

            switch (item.getItemId()) {
                case R.id.nav_home:
                    selectedFragment = createHomeFragment();
                    break;
                case R.id.nav_products:
                    selectedFragment = new ProductListFragment();
                    break;
                case R.id.nav_recommendations:
                    selectedFragment = new RecommendationFragment();
                    break;
            }

            if (selectedFragment != null) {
                FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
                transaction.replace(R.id.fragment_container, selectedFragment);
                transaction.commit();
            }

            return true;
        });

        // 默认显示首页
        if (savedInstanceState == null) {
            bottomNav.setSelectedItemId(R.id.nav_home);
        }

        // 设置客服按钮
        setupCustomerServiceButton();
    }

    private Fragment createHomeFragment() {
        // 检查是否启用个性化首页
        boolean personalizedEnabled = configManager.isPersonalizedHomepageEnabled();

        if (personalizedEnabled) {
            return RecommendationFragment.newInstance(currentUser.getId(), null);
        } else {
            return new ProductListFragment();
        }
    }

    private void setupCustomerServiceButton() {
        View customerServiceBtn = findViewById(R.id.btn_customer_service);

        // 检查是否启用在线客服
        boolean liveChatEnabled = configManager.isLiveChatEnabled();

        if (liveChatEnabled && customerServiceBtn != null) {
            customerServiceBtn.setVisibility(View.VISIBLE);
            customerServiceBtn.setOnClickListener(v -> {
                // 打开客服界面
                openCustomerService();

                // 追踪客服按钮点击
                Map<String, Object> properties = new HashMap<>();
                properties.put("feature", "live_chat");
                properties.put("page", "main");
                dataTesterService.trackEvent("customer_service_click", properties);
            });
        } else if (customerServiceBtn != null) {
            customerServiceBtn.setVisibility(View.GONE);
        }
    }

    private void openCustomerService() {
        // 实现客服功能
        Log.d(TAG, "打开在线客服");
    }

    private void trackAppLaunch() {
        if (currentUser != null) {
            Map<String, Object> properties = new HashMap<>();
            properties.put("user_id", currentUser.getId());
            properties.put("platform", "Android");
            properties.put("version", BuildConfig.VERSION_NAME);
            properties.put("user_level", currentUser.getUserLevel());

            dataTesterService.trackEvent("app_launched", properties);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        // 刷新配置（如果需要）
        if (dataTesterService.isInitialized()) {
            // 可以在这里检查配置更新
            checkConfigurationUpdates();
        }
    }

    private void checkConfigurationUpdates() {
        // 检查是否有配置更新
        // 这里可以实现配置热更新逻辑
    }
}
```

## 7. 部署和监控

### 环境变量配置
```bash
# .env.production
REACT_APP_DATATESTER_KEY=your_production_app_key
REACT_APP_API_BASE_URL=https://api.yourapp.com

# .env.development  
REACT_APP_DATATESTER_KEY=your_development_app_key
REACT_APP_API_BASE_URL=https://dev-api.yourapp.com
```

### 监控脚本
```javascript
// utils/ExperimentMonitor.js
class ExperimentMonitor {
  static checkExperimentHealth() {
    const experiments = DataTester.getAllExperiments();
    
    experiments.forEach(exp => {
      const metrics = DataTester.getExperimentMetrics(exp.experiment_key);
      
      if (metrics.error_rate > 0.05) {
        console.warn(`实验 ${exp.experiment_key} 错误率过高: ${metrics.error_rate}`);
      }
      
      if (metrics.sample_size < 100) {
        console.warn(`实验 ${exp.experiment_key} 样本量不足: ${metrics.sample_size}`);
      }
    });
  }
}

// 定期检查实验健康度
setInterval(ExperimentMonitor.checkExperimentHealth, 60000); // 每分钟检查一次
```

这个完整的示例展示了如何在实际项目中集成和使用火山引擎的云控下发和实验下发功能，包括了从初始化到具体业务场景的完整实现。
