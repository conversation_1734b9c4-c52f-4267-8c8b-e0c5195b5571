# 火山引擎云控下发和实验下发实战示例

## 项目概述

本示例展示如何在一个电商应用中集成火山引擎的云控下发和A/B测试功能，包括：
- 商品页面UI优化实验
- 购买流程优化
- 个性化推荐算法测试
- 动态配置管理

## 项目结构

```
ecommerce-app/
├── src/
│   ├── components/
│   │   ├── ProductCard.js
│   │   ├── CheckoutFlow.js
│   │   └── RecommendationList.js
│   ├── services/
│   │   ├── DataTesterService.js
│   │   ├── ProductService.js
│   │   └── UserService.js
│   ├── utils/
│   │   ├── ExperimentTracker.js
│   │   └── ConfigManager.js
│   └── App.js
├── config/
│   └── datatester.config.js
└── package.json
```

## 1. 初始化配置

### config/datatester.config.js
```javascript
export const DataTesterConfig = {
  app_key: process.env.REACT_APP_DATATESTER_KEY,
  channel: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  debug: process.env.NODE_ENV !== 'production',
  timeout: 5000,
  cache_enabled: true,
  auto_track: true,
  
  // 实验配置
  experiments: {
    PRODUCT_CARD_DESIGN: 'product_card_design_test',
    CHECKOUT_FLOW: 'checkout_flow_optimization',
    RECOMMENDATION_ALGORITHM: 'recommendation_algo_test',
    PRICING_STRATEGY: 'dynamic_pricing_test'
  },
  
  // Feature Flag配置
  features: {
    NEW_UI_THEME: 'new_ui_theme',
    ADVANCED_SEARCH: 'advanced_search_enabled',
    LIVE_CHAT: 'live_chat_support',
    PERSONALIZED_HOMEPAGE: 'personalized_homepage'
  }
};
```

### services/DataTesterService.js
```javascript
import { DataTesterConfig } from '../config/datatester.config.js';

class DataTesterService {
  constructor() {
    this.isInitialized = false;
    this.currentUserId = null;
    this.userAttributes = {};
  }

  async initialize() {
    try {
      await DataTester.init(DataTesterConfig);
      this.isInitialized = true;
      console.log('DataTester SDK 初始化成功');
      
      // 设置配置更新回调
      DataTester.onConfigUpdate(this.handleConfigUpdate.bind(this));
      
    } catch (error) {
      console.error('DataTester SDK 初始化失败:', error);
    }
  }

  setUser(userId, attributes = {}) {
    this.currentUserId = userId;
    this.userAttributes = attributes;
    
    if (this.isInitialized) {
      DataTester.setUserId(userId);
      DataTester.setUserProperty(userId, attributes);
    }
  }

  // 获取Feature Flag
  getFeatureFlag(flagKey, defaultValue) {
    if (!this.isInitialized || !this.currentUserId) {
      return defaultValue;
    }

    try {
      return DataTester.getFeatureFlag(
        flagKey,
        defaultValue,
        this.currentUserId,
        this.userAttributes
      );
    } catch (error) {
      console.error(`获取Feature Flag失败: ${flagKey}`, error);
      return defaultValue;
    }
  }

  // 获取实验配置
  getExperimentConfig(experimentKey, defaultConfig) {
    if (!this.isInitialized || !this.currentUserId) {
      return { ...defaultConfig, variant_name: 'control' };
    }

    try {
      return DataTester.getExperimentConfig(
        experimentKey,
        defaultConfig,
        this.currentUserId,
        this.userAttributes
      );
    } catch (error) {
      console.error(`获取实验配置失败: ${experimentKey}`, error);
      return { ...defaultConfig, variant_name: 'control' };
    }
  }

  // 追踪事件
  trackEvent(eventName, properties = {}) {
    if (!this.isInitialized) return;

    try {
      DataTester.trackEvent(eventName, {
        ...properties,
        user_id: this.currentUserId,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error(`事件追踪失败: ${eventName}`, error);
    }
  }

  // 追踪实验曝光
  trackExposure(experimentKey, properties = {}) {
    if (!this.isInitialized || !this.currentUserId) return;

    try {
      DataTester.trackExposure(experimentKey, this.currentUserId, properties);
    } catch (error) {
      console.error(`曝光追踪失败: ${experimentKey}`, error);
    }
  }

  handleConfigUpdate(updatedConfigs) {
    console.log('配置已更新:', updatedConfigs);
    // 触发应用重新渲染或配置更新
    window.dispatchEvent(new CustomEvent('datatester-config-updated', {
      detail: updatedConfigs
    }));
  }
}

export default new DataTesterService();
```

## 2. 商品卡片设计实验

### components/ProductCard.js
```javascript
import React, { useEffect, useState } from 'react';
import DataTesterService from '../services/DataTesterService';
import { DataTesterConfig } from '../config/datatester.config';

const ProductCard = ({ product, onAddToCart }) => {
  const [cardConfig, setCardConfig] = useState({
    layout: 'vertical',
    showRating: true,
    showDiscount: true,
    buttonStyle: 'primary',
    variant_name: 'control'
  });

  useEffect(() => {
    // 获取实验配置
    const config = DataTesterService.getExperimentConfig(
      DataTesterConfig.experiments.PRODUCT_CARD_DESIGN,
      {
        layout: 'vertical',
        showRating: true,
        showDiscount: true,
        buttonStyle: 'primary'
      }
    );
    
    setCardConfig(config);

    // 追踪曝光
    DataTesterService.trackExposure(
      DataTesterConfig.experiments.PRODUCT_CARD_DESIGN,
      {
        product_id: product.id,
        page: 'product_list'
      }
    );
  }, [product.id]);

  const handleAddToCart = () => {
    // 追踪点击事件
    DataTesterService.trackEvent('add_to_cart_click', {
      product_id: product.id,
      experiment: DataTesterConfig.experiments.PRODUCT_CARD_DESIGN,
      variant: cardConfig.variant_name,
      button_style: cardConfig.buttonStyle
    });

    onAddToCart(product);
  };

  const cardClassName = `product-card ${cardConfig.layout} ${cardConfig.variant_name}`;
  const buttonClassName = `add-to-cart-btn ${cardConfig.buttonStyle}`;

  return (
    <div className={cardClassName}>
      <img src={product.image} alt={product.name} />
      
      <div className="product-info">
        <h3>{product.name}</h3>
        
        {cardConfig.showRating && (
          <div className="rating">
            ⭐ {product.rating} ({product.reviewCount} 评价)
          </div>
        )}
        
        <div className="price">
          <span className="current-price">¥{product.price}</span>
          {cardConfig.showDiscount && product.originalPrice > product.price && (
            <span className="original-price">¥{product.originalPrice}</span>
          )}
        </div>
        
        <button 
          className={buttonClassName}
          onClick={handleAddToCart}
        >
          {cardConfig.layout === 'horizontal' ? '购买' : '加入购物车'}
        </button>
      </div>
    </div>
  );
};

export default ProductCard;
```

## 3. 购买流程优化实验

### components/CheckoutFlow.js
```javascript
import React, { useEffect, useState } from 'react';
import DataTesterService from '../services/DataTesterService';
import { DataTesterConfig } from '../config/datatester.config';

const CheckoutFlow = ({ cartItems, onComplete }) => {
  const [flowConfig, setFlowConfig] = useState({
    steps: 3,
    layout: 'vertical',
    showProgress: true,
    guestCheckout: false,
    variant_name: 'control'
  });

  const [currentStep, setCurrentStep] = useState(1);

  useEffect(() => {
    // 获取购买流程实验配置
    const config = DataTesterService.getExperimentConfig(
      DataTesterConfig.experiments.CHECKOUT_FLOW,
      {
        steps: 3,
        layout: 'vertical',
        showProgress: true,
        guestCheckout: false
      }
    );
    
    setFlowConfig(config);

    // 追踪购买流程开始
    DataTesterService.trackEvent('checkout_started', {
      experiment: DataTesterConfig.experiments.CHECKOUT_FLOW,
      variant: config.variant_name,
      cart_value: cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0),
      item_count: cartItems.length
    });

    // 追踪曝光
    DataTesterService.trackExposure(
      DataTesterConfig.experiments.CHECKOUT_FLOW,
      {
        page: 'checkout',
        cart_value: cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0)
      }
    );
  }, [cartItems]);

  const handleStepComplete = (step) => {
    DataTesterService.trackEvent('checkout_step_completed', {
      step: step,
      experiment: DataTesterConfig.experiments.CHECKOUT_FLOW,
      variant: flowConfig.variant_name
    });

    if (step < flowConfig.steps) {
      setCurrentStep(step + 1);
    } else {
      handleCheckoutComplete();
    }
  };

  const handleCheckoutComplete = () => {
    const totalValue = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
    
    // 追踪转化事件
    DataTesterService.trackEvent('purchase_completed', {
      experiment: DataTesterConfig.experiments.CHECKOUT_FLOW,
      variant: flowConfig.variant_name,
      order_value: totalValue,
      item_count: cartItems.length
    });

    onComplete();
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <ShippingInfo onComplete={() => handleStepComplete(1)} />;
      case 2:
        if (flowConfig.steps === 2) {
          return <PaymentInfo onComplete={() => handleStepComplete(2)} />;
        }
        return <PaymentMethod onComplete={() => handleStepComplete(2)} />;
      case 3:
        return <OrderReview onComplete={() => handleStepComplete(3)} />;
      default:
        return null;
    }
  };

  return (
    <div className={`checkout-flow ${flowConfig.layout} ${flowConfig.variant_name}`}>
      {flowConfig.showProgress && (
        <div className="progress-bar">
          {Array.from({ length: flowConfig.steps }, (_, i) => (
            <div 
              key={i} 
              className={`step ${i + 1 <= currentStep ? 'active' : ''}`}
            >
              {i + 1}
            </div>
          ))}
        </div>
      )}

      <div className="step-content">
        {renderStep()}
      </div>

      {flowConfig.guestCheckout && currentStep === 1 && (
        <div className="guest-checkout-option">
          <button onClick={() => handleStepComplete(1)}>
            游客结账
          </button>
        </div>
      )}
    </div>
  );
};

// 子组件示例
const ShippingInfo = ({ onComplete }) => (
  <div className="shipping-info">
    <h3>配送信息</h3>
    {/* 配送表单 */}
    <button onClick={onComplete}>下一步</button>
  </div>
);

const PaymentMethod = ({ onComplete }) => (
  <div className="payment-method">
    <h3>支付方式</h3>
    {/* 支付方式选择 */}
    <button onClick={onComplete}>下一步</button>
  </div>
);

const PaymentInfo = ({ onComplete }) => (
  <div className="payment-info">
    <h3>支付信息</h3>
    {/* 支付信息表单 */}
    <button onClick={onComplete}>完成支付</button>
  </div>
);

const OrderReview = ({ onComplete }) => (
  <div className="order-review">
    <h3>订单确认</h3>
    {/* 订单详情 */}
    <button onClick={onComplete}>确认订单</button>
  </div>
);

export default CheckoutFlow;
```

## 4. 推荐算法实验

### components/RecommendationList.js
```javascript
import React, { useEffect, useState } from 'react';
import DataTesterService from '../services/DataTesterService';
import ProductService from '../services/ProductService';
import { DataTesterConfig } from '../config/datatester.config';

const RecommendationList = ({ userId, currentProductId }) => {
  const [recommendations, setRecommendations] = useState([]);
  const [algorithmConfig, setAlgorithmConfig] = useState({
    algorithm: 'collaborative_filtering',
    count: 6,
    include_popular: true,
    variant_name: 'control'
  });

  useEffect(() => {
    loadRecommendations();
  }, [userId, currentProductId]);

  const loadRecommendations = async () => {
    // 获取推荐算法实验配置
    const config = DataTesterService.getExperimentConfig(
      DataTesterConfig.experiments.RECOMMENDATION_ALGORITHM,
      {
        algorithm: 'collaborative_filtering',
        count: 6,
        include_popular: true
      }
    );
    
    setAlgorithmConfig(config);

    try {
      // 根据实验配置获取推荐结果
      const recs = await ProductService.getRecommendations(
        userId,
        currentProductId,
        config
      );
      
      setRecommendations(recs);

      // 追踪推荐曝光
      DataTesterService.trackExposure(
        DataTesterConfig.experiments.RECOMMENDATION_ALGORITHM,
        {
          algorithm: config.algorithm,
          recommendation_count: recs.length,
          current_product_id: currentProductId
        }
      );

      // 追踪推荐展示事件
      DataTesterService.trackEvent('recommendations_shown', {
        experiment: DataTesterConfig.experiments.RECOMMENDATION_ALGORITHM,
        variant: config.variant_name,
        algorithm: config.algorithm,
        product_ids: recs.map(r => r.id),
        count: recs.length
      });

    } catch (error) {
      console.error('加载推荐失败:', error);
    }
  };

  const handleRecommendationClick = (product, index) => {
    // 追踪推荐点击
    DataTesterService.trackEvent('recommendation_click', {
      experiment: DataTesterConfig.experiments.RECOMMENDATION_ALGORITHM,
      variant: algorithmConfig.variant_name,
      algorithm: algorithmConfig.algorithm,
      product_id: product.id,
      position: index,
      current_product_id: currentProductId
    });
  };

  return (
    <div className="recommendation-list">
      <h3>为您推荐</h3>
      <div className="recommendations">
        {recommendations.map((product, index) => (
          <div 
            key={product.id} 
            className="recommendation-item"
            onClick={() => handleRecommendationClick(product, index)}
          >
            <img src={product.image} alt={product.name} />
            <div className="product-info">
              <h4>{product.name}</h4>
              <span className="price">¥{product.price}</span>
              {algorithmConfig.algorithm === 'content_based' && (
                <span className="similarity">相似度: {product.similarity}%</span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecommendationList;
```

## 5. 动态配置管理

### utils/ConfigManager.js
```javascript
import DataTesterService from '../services/DataTesterService';
import { DataTesterConfig } from '../config/datatester.config';

class ConfigManager {
  constructor() {
    this.configs = {};
    this.listeners = [];
    
    // 监听配置更新
    window.addEventListener('datatester-config-updated', this.handleConfigUpdate.bind(this));
  }

  // 获取UI主题配置
  getUITheme() {
    const isNewTheme = DataTesterService.getFeatureFlag(
      DataTesterConfig.features.NEW_UI_THEME,
      false
    );

    return {
      theme: isNewTheme ? 'modern' : 'classic',
      primaryColor: isNewTheme ? '#6366f1' : '#1890ff',
      borderRadius: isNewTheme ? '12px' : '6px',
      fontFamily: isNewTheme ? 'Inter, sans-serif' : 'Arial, sans-serif'
    };
  }

  // 获取搜索功能配置
  getSearchConfig() {
    const advancedSearchEnabled = DataTesterService.getFeatureFlag(
      DataTesterConfig.features.ADVANCED_SEARCH,
      false
    );

    return {
      enableAdvancedSearch: advancedSearchEnabled,
      enableAutoComplete: true,
      enableSearchHistory: advancedSearchEnabled,
      maxSuggestions: advancedSearchEnabled ? 10 : 5
    };
  }

  // 获取客服配置
  getCustomerServiceConfig() {
    const liveChatEnabled = DataTesterService.getFeatureFlag(
      DataTesterConfig.features.LIVE_CHAT,
      false
    );

    return {
      enableLiveChat: liveChatEnabled,
      enableChatBot: true,
      workingHours: liveChatEnabled ? '9:00-21:00' : '9:00-18:00',
      maxWaitTime: liveChatEnabled ? 30 : 60 // 秒
    };
  }

  // 获取个性化首页配置
  getHomepageConfig() {
    const personalizedEnabled = DataTesterService.getFeatureFlag(
      DataTesterConfig.features.PERSONALIZED_HOMEPAGE,
      false
    );

    return {
      enablePersonalization: personalizedEnabled,
      showRecommendations: personalizedEnabled,
      showRecentlyViewed: true,
      maxRecommendations: personalizedEnabled ? 12 : 6
    };
  }

  // 获取所有配置
  getAllConfigs() {
    return {
      ui: this.getUITheme(),
      search: this.getSearchConfig(),
      customerService: this.getCustomerServiceConfig(),
      homepage: this.getHomepageConfig()
    };
  }

  // 添加配置变更监听器
  addConfigListener(callback) {
    this.listeners.push(callback);
  }

  // 移除配置变更监听器
  removeConfigListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  // 处理配置更新
  handleConfigUpdate(event) {
    const updatedConfigs = this.getAllConfigs();
    this.configs = updatedConfigs;
    
    // 通知所有监听器
    this.listeners.forEach(callback => {
      try {
        callback(updatedConfigs);
      } catch (error) {
        console.error('配置监听器执行失败:', error);
      }
    });
  }
}

export default new ConfigManager();
```

## 6. 应用入口集成

### App.js
```javascript
import React, { useEffect, useState } from 'react';
import DataTesterService from './services/DataTesterService';
import ConfigManager from './utils/ConfigManager';
import ProductCard from './components/ProductCard';
import CheckoutFlow from './components/CheckoutFlow';
import RecommendationList from './components/RecommendationList';

function App() {
  const [isSDKReady, setIsSDKReady] = useState(false);
  const [configs, setConfigs] = useState({});
  const [user, setUser] = useState(null);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // 初始化DataTester SDK
      await DataTesterService.initialize();
      
      // 模拟用户登录
      const userData = {
        id: 'user_12345',
        attributes: {
          user_level: 'premium',
          registration_date: '2023-01-15',
          city: 'Beijing',
          age: 28,
          gender: 'male'
        }
      };
      
      DataTesterService.setUser(userData.id, userData.attributes);
      setUser(userData);
      
      // 获取初始配置
      const initialConfigs = ConfigManager.getAllConfigs();
      setConfigs(initialConfigs);
      
      // 监听配置变更
      ConfigManager.addConfigListener(setConfigs);
      
      setIsSDKReady(true);
      
      // 追踪应用启动事件
      DataTesterService.trackEvent('app_launched', {
        user_id: userData.id,
        platform: 'web',
        version: '1.0.0'
      });
      
    } catch (error) {
      console.error('应用初始化失败:', error);
    }
  };

  if (!isSDKReady) {
    return <div className="loading">正在初始化...</div>;
  }

  return (
    <div 
      className="app" 
      style={{
        '--primary-color': configs.ui?.primaryColor,
        '--border-radius': configs.ui?.borderRadius,
        fontFamily: configs.ui?.fontFamily
      }}
    >
      <header className={`app-header ${configs.ui?.theme}`}>
        <h1>电商示例应用</h1>
        {configs.customerService?.enableLiveChat && (
          <button className="live-chat-btn">在线客服</button>
        )}
      </header>

      <main>
        {configs.homepage?.enablePersonalization && (
          <section className="personalized-section">
            <h2>为您推荐</h2>
            <RecommendationList 
              userId={user?.id} 
              currentProductId={null}
            />
          </section>
        )}

        <section className="products">
          <h2>热门商品</h2>
          {/* 商品列表 */}
        </section>
      </main>
    </div>
  );
}

export default App;
```

## 7. 部署和监控

### 环境变量配置
```bash
# .env.production
REACT_APP_DATATESTER_KEY=your_production_app_key
REACT_APP_API_BASE_URL=https://api.yourapp.com

# .env.development  
REACT_APP_DATATESTER_KEY=your_development_app_key
REACT_APP_API_BASE_URL=https://dev-api.yourapp.com
```

### 监控脚本
```javascript
// utils/ExperimentMonitor.js
class ExperimentMonitor {
  static checkExperimentHealth() {
    const experiments = DataTester.getAllExperiments();
    
    experiments.forEach(exp => {
      const metrics = DataTester.getExperimentMetrics(exp.experiment_key);
      
      if (metrics.error_rate > 0.05) {
        console.warn(`实验 ${exp.experiment_key} 错误率过高: ${metrics.error_rate}`);
      }
      
      if (metrics.sample_size < 100) {
        console.warn(`实验 ${exp.experiment_key} 样本量不足: ${metrics.sample_size}`);
      }
    });
  }
}

// 定期检查实验健康度
setInterval(ExperimentMonitor.checkExperimentHealth, 60000); // 每分钟检查一次
```

这个完整的示例展示了如何在实际项目中集成和使用火山引擎的云控下发和实验下发功能，包括了从初始化到具体业务场景的完整实现。
