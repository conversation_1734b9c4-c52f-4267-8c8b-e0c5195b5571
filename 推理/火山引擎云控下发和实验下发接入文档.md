# 火山引擎云控下发和实验下发接入文档

## 概述

火山引擎提供了两种主要的配置下发能力：

1. **云控下发（FeatureFlag）** - 用于动态配置、人群定向发布、实验全量等配置下发
2. **实验下发（A/B测试）** - 用于验证评估不同版本/策略的效果

## 一、云控下发（FeatureFlag）

### 1.1 产品介绍

FeatureFlag（智能发布）是一种结合功能开关+动态配置+灰度发布+配置管理的工具，主要用于：

- 动态配置下发
- 人群定向发布
- 实验全量
- 配置治理
- 快速回滚

### 1.2 核心特性

- **无需发版**：通过动态控制实现配置变更，无需重新部署代码
- **灵活控制**：对生效流量、目标受众进行灵活控制
- **生命周期管理**：提供配置参数使用情况的全生命周期管理
- **渐进式发布**：支持小流量验证，避免全量风险

### 1.3 应用场景

- 功能开关控制
- 配置参数动态调整
- 灰度发布
- 紧急回滚
- 大模型配置托管

## 二、实验下发（A/B测试）

### 2.1 产品介绍

A/B测试（DataTester）是火山引擎提供的实验平台，用于验证不同策略的效果。支持：

- 客户端实验
- 服务端实验
- 大模型实验

### 2.2 实验类型

#### 客户端实验

- **特点**：APP唤起时，AB相关配置即需生效
- **依赖**：客户端SDK
- **适用场景**：UI界面变更、交互逻辑调整

#### 服务端实验

- **特点**：通过服务端获取实验分组信息并控制配置生效
- **优势**：命中和曝光逻辑在服务端处理
- **适用场景**：算法策略、推荐逻辑

### 2.3 支持平台

- Android SDK（本文档重点）

## 三、Android SDK集成指南

### 3.1 前置准备

1. **服务开通**

   - 登录火山引擎控制台
   - 开通A/B测试服务
   - 获取AppKey和相关配置
2. **权限配置**

   - 配置API访问密钥（Access Key）
   - 设置应用权限

### 3.2 Android SDK集成（重点）

#### 环境要求

- Android API Level 16 (Android 4.1) 及以上
- 支持 AndroidX
- 建议使用 Gradle 7.0+ 和 AGP 7.0+

#### 依赖添加

**方式一：Maven Central（推荐）**

```gradle
// app/build.gradle
dependencies {
    implementation 'com.volcengine:datatester-android-sdk:2.1.5'

    // 可选：如果需要网络请求优化
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'

    // 可选：如果需要JSON解析优化
    implementation 'com.google.code.gson:gson:2.10.1'
}
```

**方式二：本地AAR文件**

```gradle
// 1. 将 datatester-android-sdk-2.1.5.aar 放入 app/libs/ 目录
// 2. 在 app/build.gradle 中添加
dependencies {
    implementation files('libs/datatester-android-sdk-2.1.5.aar')
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'
    implementation 'com.google.code.gson:gson:2.10.1'
}
```

#### 权限配置

在 `AndroidManifest.xml` 中添加必要权限：

```xml
<!-- 网络权限（必需） -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

<!-- 可选权限，用于更精准的用户识别 -->
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
```

#### 混淆配置

在 `proguard-rules.pro` 中添加：

```proguard
# DataTester SDK
-keep class com.volcengine.datatester.** { *; }
-keep interface com.volcengine.datatester.** { *; }

# 网络请求相关
-keep class okhttp3.** { *; }
-keep class com.google.gson.** { *; }

# 避免混淆实验配置相关的类
-keepclassmembers class * {
    @com.volcengine.datatester.annotation.* <fields>;
    @com.volcengine.datatester.annotation.* <methods>;
}
```

#### 初始化配置

**Application类中初始化（推荐）**

```java
public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        initDataTester();
    }

    private void initDataTester() {
        DataTesterConfig config = new DataTesterConfig.Builder()
            .setAppKey("your_app_key_here")
            .setChannel(BuildConfig.DEBUG ? "debug" : "release")
            .setUserId(getCurrentUserId()) // 如果已知用户ID
            .setDebugMode(BuildConfig.DEBUG)
            .setLogLevel(BuildConfig.DEBUG ? LogLevel.DEBUG : LogLevel.ERROR)
            .setNetworkTimeout(10000) // 网络超时10秒
            .setCacheEnabled(true) // 启用本地缓存
            .setAutoTrackEnabled(true) // 自动追踪应用生命周期
            .setServerUrl("https://datarangers.com.cn") // 可选，自定义服务器地址
            .build();

        DataTester.init(this, config, new InitCallback() {
            @Override
            public void onSuccess() {
                Log.d("DataTester", "SDK初始化成功");
                // 可以开始使用SDK功能
            }

            @Override
            public void onFailure(String error) {
                Log.e("DataTester", "SDK初始化失败: " + error);
                // 使用默认配置继续运行
            }
        });
    }

    private String getCurrentUserId() {
        // 从SharedPreferences或其他地方获取用户ID
        SharedPreferences prefs = getSharedPreferences("user_prefs", MODE_PRIVATE);
        return prefs.getString("user_id", null);
    }
}
```

**在Activity中延迟初始化**

```java
public class MainActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 检查SDK是否已初始化
        if (!DataTester.isInitialized()) {
            initDataTesterIfNeeded();
        }

        // 设置用户信息
        setupUserInfo();
    }

    private void initDataTesterIfNeeded() {
        DataTesterConfig config = new DataTesterConfig.Builder()
            .setAppKey(getString(R.string.datatester_app_key))
            .setChannel(BuildConfig.FLAVOR) // 使用构建变体作为渠道
            .setDebugMode(BuildConfig.DEBUG)
            .build();

        DataTester.init(getApplicationContext(), config);
    }

    private void setupUserInfo() {
        String userId = UserManager.getInstance().getCurrentUserId();
        if (!TextUtils.isEmpty(userId)) {
            DataTester.setUserId(userId);

            // 设置用户属性
            Map<String, Object> userProperties = new HashMap<>();
            userProperties.put("user_level", UserManager.getInstance().getUserLevel());
            userProperties.put("registration_date", UserManager.getInstance().getRegistrationDate());
            userProperties.put("city", LocationManager.getInstance().getCurrentCity());

            DataTester.setUserProperties(userProperties);
        }
    }
}
```

#### 获取实验配置

**基础用法**

```java
// 获取字符串类型的实验参数
String buttonColor = DataTester.getExperimentVariableValue(
    "button_color_test",
    "button_color",
    "#1890ff", // 默认值
    userId
);

// 获取布尔类型的实验参数
boolean isNewUIEnabled = DataTester.getExperimentVariableValue(
    "new_ui_test",
    "enabled",
    false, // 默认值
    userId
);

// 获取数值类型的实验参数
int maxRetryCount = DataTester.getExperimentVariableValue(
    "retry_strategy_test",
    "max_retry",
    3, // 默认值
    userId
);
```

**获取完整实验配置**

```java
// 获取实验的所有配置参数
ExperimentConfig config = DataTester.getExperimentConfig(
    "checkout_flow_test",
    userId
);

if (config != null) {
    String variantName = config.getVariantName(); // 获取分组名称
    Map<String, Object> variables = config.getVariables(); // 获取所有变量

    // 使用配置
    String flowType = (String) variables.get("flow_type");
    Integer stepCount = (Integer) variables.get("step_count");
    Boolean showProgress = (Boolean) variables.get("show_progress");

    Log.d("Experiment", "用户分组: " + variantName + ", 流程类型: " + flowType);
}
```

**异步获取配置（推荐用于网络敏感场景）**

```java
// 异步获取实验配置，避免阻塞主线程
DataTester.getExperimentConfigAsync("product_recommendation_test", userId,
    new ExperimentCallback<ExperimentConfig>() {
        @Override
        public void onSuccess(ExperimentConfig config) {
            // 在主线程中处理结果
            runOnUiThread(() -> {
                applyExperimentConfig(config);
            });
        }

        @Override
        public void onFailure(String error) {
            Log.e("DataTester", "获取实验配置失败: " + error);
            // 使用默认配置
            applyDefaultConfig();
        }
    });
```

#### FeatureFlag使用

**获取Feature Flag配置**

```java
// 获取布尔类型的Feature Flag
boolean isNewThemeEnabled = DataTester.getFeatureFlag(
    "new_theme_enabled",
    false, // 默认值
    userId
);

// 获取字符串类型的Feature Flag
String apiEndpoint = DataTester.getFeatureFlag(
    "api_endpoint",
    "https://api.default.com", // 默认值
    userId
);

// 获取JSON对象类型的Feature Flag
String configJson = DataTester.getFeatureFlag(
    "ui_config",
    "{\"theme\":\"light\",\"animation\":true}", // 默认JSON字符串
    userId
);

// 解析JSON配置
try {
    JSONObject uiConfig = new JSONObject(configJson);
    String theme = uiConfig.getString("theme");
    boolean animationEnabled = uiConfig.getBoolean("animation");

    applyUIConfig(theme, animationEnabled);
} catch (JSONException e) {
    Log.e("DataTester", "解析UI配置失败", e);
}
```

**带用户属性的配置获取**

```java
// 设置用户属性用于更精准的配置下发
Map<String, Object> userAttributes = new HashMap<>();
userAttributes.put("user_level", "premium");
userAttributes.put("city", "Beijing");
userAttributes.put("app_version", BuildConfig.VERSION_NAME);
userAttributes.put("device_type", "Android");

// 获取基于用户属性的Feature Flag
boolean premiumFeatureEnabled = DataTester.getFeatureFlagWithAttributes(
    "premium_feature_enabled",
    false,
    userId,
    userAttributes
);
```

#### 事件追踪

**基础事件追踪**

```java
// 追踪用户行为事件
Map<String, Object> eventProperties = new HashMap<>();
eventProperties.put("product_id", "12345");
eventProperties.put("category", "electronics");
eventProperties.put("price", 299.99);

DataTester.trackEvent("product_view", eventProperties);
```

**实验曝光追踪**

```java
// 当用户看到实验内容时追踪曝光
DataTester.trackExposure("button_color_test", userId, new HashMap<String, Object>() {{
    put("page", "product_detail");
    put("position", "bottom");
}});
```

**转化事件追踪**

```java
// 追踪关键转化事件
Map<String, Object> conversionData = new HashMap<>();
conversionData.put("order_id", "ORDER_123456");
conversionData.put("amount", 199.99);
conversionData.put("currency", "CNY");
conversionData.put("experiment", "checkout_flow_test");

DataTester.trackEvent("purchase_completed", conversionData);
```

#### 高级功能

**批量获取配置**

```java
// 一次性获取多个Feature Flag，减少网络请求
List<String> flagKeys = Arrays.asList(
    "new_theme_enabled",
    "advanced_search_enabled",
    "live_chat_enabled"
);

DataTester.getMultipleFeatureFlags(flagKeys, userId, new FeatureFlagCallback() {
    @Override
    public void onSuccess(Map<String, Object> flags) {
        boolean newTheme = (Boolean) flags.get("new_theme_enabled");
        boolean advancedSearch = (Boolean) flags.get("advanced_search_enabled");
        boolean liveChat = (Boolean) flags.get("live_chat_enabled");

        // 应用所有配置
        applyFeatureFlags(newTheme, advancedSearch, liveChat);
    }

    @Override
    public void onFailure(String error) {
        Log.e("DataTester", "批量获取Feature Flag失败: " + error);
    }
});
```

**配置变更监听**

```java
// 监听配置实时更新
DataTester.addConfigChangeListener(new ConfigChangeListener() {
    @Override
    public void onConfigChanged(String key, Object newValue, Object oldValue) {
        Log.d("DataTester", "配置更新: " + key + " 从 " + oldValue + " 变为 " + newValue);

        // 根据配置变更更新UI
        if ("new_theme_enabled".equals(key)) {
            updateTheme((Boolean) newValue);
        }
    }
});
```

**强制刷新配置（用于调试）**

```java
// 强制从服务器刷新配置，忽略本地缓存
DataTester.refreshConfigs(new RefreshCallback() {
    @Override
    public void onSuccess() {
        Log.d("DataTester", "配置刷新成功");
        // 重新获取最新配置
        reloadAllConfigs();
    }

    @Override
    public void onFailure(String error) {
        Log.e("DataTester", "配置刷新失败: " + error);
    }
});
```

## 四、配置管理

### 4.1 FeatureFlag配置

1. **创建Feature**

   - 登录火山引擎控制台
   - 进入A/B测试 > FeatureFlag
   - 创建新的Feature配置
2. **配置参数**

   - 设置配置键值
   - 定义目标人群
   - 配置生效规则
3. **发布管理**

   - 小流量验证
   - 逐步扩量
   - 全量发布

### 4.2 A/B实验配置

1. **创建实验**

   - 定义实验目标
   - 设置实验组和对照组
   - 配置分流规则
2. **参数配置**

   - 设置实验参数
   - 定义变量类型
   - 配置默认值
3. **实验管理**

   - 启动实验
   - 监控数据
   - 分析结果

## 五、最佳实践

### 5.1 集成建议

1. **渐进式接入**：先接入基础功能，再扩展高级特性
2. **异常处理**：做好网络异常和配置异常的降级处理
3. **缓存策略**：合理使用本地缓存，提升用户体验
4. **日志监控**：完善日志记录，便于问题排查

### 5.2 性能优化

1. **异步加载**：配置获取采用异步方式
2. **批量请求**：合并多个配置请求
3. **缓存机制**：实现本地缓存和更新策略
4. **网络优化**：使用CDN加速配置下发

## 六、常见问题

### 6.1 集成问题

**Q: SDK初始化失败怎么办？**
A: 检查AppKey配置是否正确，网络连接是否正常

**Q: 获取不到实验配置？**
A: 确认实验已启动，用户ID格式正确，分流规则配置无误

### 6.2 配置问题

**Q: 配置更新不及时？**
A: 检查缓存策略，考虑调整配置拉取频率

**Q: 如何处理配置冲突？**
A: 建立配置优先级机制，制定冲突解决策略

## 七、相关链接

- [火山引擎官方文档](https://www.volcengine.com/docs)
- [A/B测试产品页](https://www.volcengine.com/product/datatester)
- [SDK下载中心](https://api.volcengine.com/api-sdk)
- [GitHub开源SDK](https://github.com/volcengine)

## 八、技术支持

如需技术支持，请通过以下方式联系：

- 官方文档：https://www.volcengine.com/docs/6287
- 技术支持：通过火山引擎控制台提交工单
- 开发者社区：https://developer.volcengine.com
