# 火山引擎云控下发和实验下发接入文档

## 概述

火山引擎提供了两种主要的配置下发能力：
1. **云控下发（FeatureFlag）** - 用于动态配置、人群定向发布、实验全量等配置下发
2. **实验下发（A/B测试）** - 用于验证评估不同版本/策略的效果

## 一、云控下发（FeatureFlag）

### 1.1 产品介绍

FeatureFlag（智能发布）是一种结合功能开关+动态配置+灰度发布+配置管理的工具，主要用于：
- 动态配置下发
- 人群定向发布
- 实验全量
- 配置治理
- 快速回滚

### 1.2 核心特性

- **无需发版**：通过动态控制实现配置变更，无需重新部署代码
- **灵活控制**：对生效流量、目标受众进行灵活控制
- **生命周期管理**：提供配置参数使用情况的全生命周期管理
- **渐进式发布**：支持小流量验证，避免全量风险

### 1.3 应用场景

- 功能开关控制
- 配置参数动态调整
- 灰度发布
- 紧急回滚
- 大模型配置托管

## 二、实验下发（A/B测试）

### 2.1 产品介绍

A/B测试（DataTester）是火山引擎提供的实验平台，用于验证不同策略的效果。支持：
- 客户端实验
- 服务端实验
- 大模型实验

### 2.2 实验类型

#### 客户端实验
- **特点**：APP唤起时，AB相关配置即需生效
- **依赖**：客户端SDK
- **适用场景**：UI界面变更、交互逻辑调整

#### 服务端实验
- **特点**：通过服务端获取实验分组信息并控制配置生效
- **优势**：命中和曝光逻辑在服务端处理
- **适用场景**：算法策略、推荐逻辑

### 2.3 支持平台

- Android SDK
- iOS SDK
- Web/JS SDK
- 小程序SDK
- Java SDK（服务端）

## 三、SDK集成指南

### 3.1 前置准备

1. **服务开通**
   - 登录火山引擎控制台
   - 开通A/B测试服务
   - 获取AppKey和相关配置

2. **权限配置**
   - 配置API访问密钥（Access Key）
   - 设置应用权限

### 3.2 Android SDK集成

#### 依赖添加
```gradle
implementation 'com.volcengine:datatester-android-sdk:latest_version'
```

#### 初始化配置
```java
// 初始化SDK
DataTesterConfig config = new DataTesterConfig.Builder()
    .setAppKey("your_app_key")
    .setChannel("your_channel")
    .build();
    
DataTester.init(this, config);
```

#### 获取实验配置
```java
// 获取实验参数
String experimentValue = DataTester.getExperimentVariableValue(
    "experiment_key", 
    "default_value", 
    userId
);
```

### 3.3 iOS SDK集成

#### 依赖添加
```ruby
# Podfile
pod 'DataTesterSDK'
```

#### 初始化配置
```objc
// 初始化SDK
DTConfig *config = [[DTConfig alloc] init];
config.appKey = @"your_app_key";
config.channel = @"your_channel";

[DataTester startWithConfig:config];
```

#### 获取实验配置
```objc
// 获取实验参数
NSString *value = [DataTester getExperimentVariableValue:@"experiment_key" 
                                            defaultValue:@"default_value" 
                                                  userId:userId];
```

### 3.4 Web/JS SDK集成

#### 引入SDK
```html
<script src="https://lf3-cdn-tos.bytescm.com/obj/static/web-sdk/datatester.js"></script>
```

#### 初始化配置
```javascript
// 初始化SDK
window.DataTester.init({
    app_key: 'your_app_key',
    channel: 'your_channel'
});
```

#### 获取实验配置
```javascript
// 获取实验参数
const value = window.DataTester.getExperimentVariableValue(
    'experiment_key',
    'default_value',
    userId
);
```

### 3.5 Java SDK集成（服务端）

#### Maven依赖
```xml
<dependency>
    <groupId>com.volcengine</groupId>
    <artifactId>datatester-java-sdk</artifactId>
    <version>2.0.22</version>
</dependency>
```

#### 初始化配置
```java
// 初始化客户端
DataTesterClient client = DataTesterClient.builder()
    .appKey("your_app_key")
    .build();
```

#### 获取实验配置
```java
// 获取实验参数
String value = client.getExperimentVariableValue(
    "experiment_key",
    "default_value",
    userId
);
```

## 四、配置管理

### 4.1 FeatureFlag配置

1. **创建Feature**
   - 登录火山引擎控制台
   - 进入A/B测试 > FeatureFlag
   - 创建新的Feature配置

2. **配置参数**
   - 设置配置键值
   - 定义目标人群
   - 配置生效规则

3. **发布管理**
   - 小流量验证
   - 逐步扩量
   - 全量发布

### 4.2 A/B实验配置

1. **创建实验**
   - 定义实验目标
   - 设置实验组和对照组
   - 配置分流规则

2. **参数配置**
   - 设置实验参数
   - 定义变量类型
   - 配置默认值

3. **实验管理**
   - 启动实验
   - 监控数据
   - 分析结果

## 五、最佳实践

### 5.1 集成建议

1. **渐进式接入**：先接入基础功能，再扩展高级特性
2. **异常处理**：做好网络异常和配置异常的降级处理
3. **缓存策略**：合理使用本地缓存，提升用户体验
4. **日志监控**：完善日志记录，便于问题排查

### 5.2 性能优化

1. **异步加载**：配置获取采用异步方式
2. **批量请求**：合并多个配置请求
3. **缓存机制**：实现本地缓存和更新策略
4. **网络优化**：使用CDN加速配置下发

## 六、常见问题

### 6.1 集成问题

**Q: SDK初始化失败怎么办？**
A: 检查AppKey配置是否正确，网络连接是否正常

**Q: 获取不到实验配置？**
A: 确认实验已启动，用户ID格式正确，分流规则配置无误

### 6.2 配置问题

**Q: 配置更新不及时？**
A: 检查缓存策略，考虑调整配置拉取频率

**Q: 如何处理配置冲突？**
A: 建立配置优先级机制，制定冲突解决策略

## 七、相关链接

- [火山引擎官方文档](https://www.volcengine.com/docs)
- [A/B测试产品页](https://www.volcengine.com/product/datatester)
- [SDK下载中心](https://api.volcengine.com/api-sdk)
- [GitHub开源SDK](https://github.com/volcengine)

## 八、技术支持

如需技术支持，请通过以下方式联系：
- 官方文档：https://www.volcengine.com/docs/6287
- 技术支持：通过火山引擎控制台提交工单
- 开发者社区：https://developer.volcengine.com
