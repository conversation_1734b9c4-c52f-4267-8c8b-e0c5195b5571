# 火山引擎云控下发和实验下发使用指南

## 目录
1. [快速开始](#快速开始)
2. [FeatureFlag使用指南](#featureflag使用指南)
3. [A/B测试使用指南](#ab测试使用指南)
4. [高级功能](#高级功能)
5. [监控与分析](#监控与分析)
6. [故障排除](#故障排除)

## 快速开始

### 第一步：账号准备
1. 注册火山引擎账号
2. 完成实名认证
3. 开通A/B测试服务
4. 获取AppKey和AccessKey

### 第二步：创建应用
```bash
# 在控制台创建应用
应用名称: MyApp
应用类型: Android/iOS/Web
包名/Bundle ID: com.example.myapp
```

### 第三步：Android SDK集成
```gradle
// app/build.gradle
dependencies {
    implementation 'com.volcengine:datatester-android-sdk:2.1.5'
}
```

```java
// Application类中初始化
public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();

        DataTesterConfig config = new DataTesterConfig.Builder()
            .setAppKey("your_app_key")
            .setChannel(BuildConfig.DEBUG ? "debug" : "release")
            .setDebugMode(BuildConfig.DEBUG)
            .build();

        DataTester.init(this, config);
    }
}
```

## FeatureFlag使用指南

### 2.1 创建Feature

#### 控制台操作
1. 登录火山引擎控制台
2. 进入 **A/B测试** > **FeatureFlag**
3. 点击 **创建Feature**

#### 配置示例
```json
{
  "feature_key": "new_ui_theme",
  "feature_name": "新UI主题",
  "description": "新版本UI主题切换",
  "type": "boolean",
  "default_value": false,
  "target_audience": {
    "percentage": 10,
    "user_groups": ["beta_users"]
  }
}
```

### 2.2 配置类型

#### Boolean类型（Android示例）
```java
// 获取布尔值配置
boolean isNewUIEnabled = DataTester.getFeatureFlag(
    "new_ui_theme",
    false, // 默认值
    userId
);

if (isNewUIEnabled) {
    // 启用新UI
    enableNewUI();
} else {
    // 使用旧UI
    useOldUI();
}

// 在Activity中的实际应用
public class MainActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 根据Feature Flag决定使用哪个布局
        boolean useNewLayout = DataTester.getFeatureFlag(
            "new_main_layout",
            false,
            getCurrentUserId()
        );

        if (useNewLayout) {
            setContentView(R.layout.activity_main_new);
        } else {
            setContentView(R.layout.activity_main_old);
        }

        setupUI();
    }
}
```

#### String类型（Android示例）
```java
// 获取字符串配置
String themeColor = DataTester.getFeatureFlag(
    "theme_color",
    "#1890ff", // 默认值
    userId
);

// 应用主题色到Android视图
View mainContainer = findViewById(R.id.main_container);
mainContainer.setBackgroundColor(Color.parseColor(themeColor));

// 或者应用到ActionBar
if (getSupportActionBar() != null) {
    getSupportActionBar().setBackgroundDrawable(
        new ColorDrawable(Color.parseColor(themeColor))
    );
}
```

#### Number类型（Android示例）
```java
// 获取数值配置
int maxRetryCount = DataTester.getFeatureFlag(
    "max_retry_count",
    3, // 默认值
    userId
);

// 在网络请求中使用重试次数
private void performNetworkRequest() {
    RetrofitClient.getInstance()
        .getApiService()
        .getData()
        .retryWhen(errors -> errors
            .zipWith(Observable.range(1, maxRetryCount), (error, retryCount) -> {
                if (retryCount < maxRetryCount) {
                    return Observable.timer(retryCount * 1000, TimeUnit.MILLISECONDS);
                } else {
                    return Observable.error(error);
                }
            })
            .flatMap(timer -> timer)
        )
        .subscribe(this::handleSuccess, this::handleError);
}
```

#### JSON类型（Android示例）
```java
// 获取JSON配置
String uiConfigJson = DataTester.getFeatureFlag(
    "ui_config",
    "{\"buttonSize\":\"medium\",\"showAnimation\":true}", // 默认JSON字符串
    userId
);

// 解析JSON配置
try {
    JSONObject uiConfig = new JSONObject(uiConfigJson);
    String buttonSize = uiConfig.getString("buttonSize");
    boolean showAnimation = uiConfig.getBoolean("showAnimation");

    // 应用UI配置
    applyUIConfig(buttonSize, showAnimation);

} catch (JSONException e) {
    Log.e("FeatureFlag", "解析UI配置失败", e);
    // 使用默认配置
    applyUIConfig("medium", true);
}

private void applyUIConfig(String buttonSize, boolean showAnimation) {
    Button actionButton = findViewById(R.id.action_button);

    // 设置按钮大小
    ViewGroup.LayoutParams params = actionButton.getLayoutParams();
    switch (buttonSize) {
        case "small":
            params.height = dpToPx(32);
            break;
        case "large":
            params.height = dpToPx(56);
            break;
        default: // medium
            params.height = dpToPx(44);
            break;
    }
    actionButton.setLayoutParams(params);

    // 设置动画
    if (showAnimation) {
        actionButton.setOnClickListener(v -> {
            v.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction(() -> {
                    v.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start();
                })
                .start();
        });
    }
}

private int dpToPx(int dp) {
    return (int) (dp * getResources().getDisplayMetrics().density);
}
```

### 2.3 人群定向

#### 按用户属性
```json
{
  "target_rules": [
    {
      "condition": "user_level >= 5",
      "value": true
    },
    {
      "condition": "city in ['北京', '上海', '深圳']",
      "value": true
    }
  ]
}
```

#### 按设备属性
```json
{
  "target_rules": [
    {
      "condition": "device_type == 'iOS'",
      "value": true
    },
    {
      "condition": "app_version >= '2.0.0'",
      "value": true
    }
  ]
}
```

### 2.4 灰度发布

#### 阶段性发布
```bash
阶段1: 5%用户  -> 观察1天
阶段2: 20%用户 -> 观察1天  
阶段3: 50%用户 -> 观察1天
阶段4: 100%用户 -> 全量发布
```

#### 代码示例
```javascript
// 检查Feature状态
const featureStatus = DataTester.getFeatureStatus('new_feature');

switch(featureStatus.stage) {
  case 'testing':
    // 测试阶段，记录详细日志
    enableDetailedLogging();
    break;
  case 'rolling_out':
    // 灰度阶段，监控关键指标
    monitorKeyMetrics();
    break;
  case 'fully_released':
    // 全量发布，正常运行
    break;
}
```

## A/B测试使用指南

### 3.1 创建实验

#### 实验设计
```json
{
  "experiment_name": "购买按钮颜色测试",
  "hypothesis": "红色按钮比蓝色按钮有更高的点击率",
  "primary_metric": "button_click_rate",
  "secondary_metrics": ["conversion_rate", "revenue_per_user"],
  "traffic_allocation": 20,
  "variants": [
    {
      "name": "control",
      "allocation": 50,
      "config": {"button_color": "#1890ff"}
    },
    {
      "name": "treatment",
      "allocation": 50,
      "config": {"button_color": "#ff4d4f"}
    }
  ]
}
```

#### Android代码实现
```java
public class ProductDetailActivity extends AppCompatActivity {
    private String userId;
    private String experimentKey = "button_color_test";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_product_detail);

        userId = UserManager.getInstance().getCurrentUserId();
        setupExperiment();
    }

    private void setupExperiment() {
        // 获取实验配置
        Map<String, Object> defaultConfig = new HashMap<>();
        defaultConfig.put("button_color", "#1890ff");
        defaultConfig.put("button_text", "立即购买");

        ExperimentConfig experimentConfig = DataTester.getExperimentConfig(
            experimentKey,
            userId
        );

        String buttonColor = "#1890ff"; // 默认值
        String buttonText = "立即购买"; // 默认值
        String variantName = "control"; // 默认分组

        if (experimentConfig != null) {
            Map<String, Object> variables = experimentConfig.getVariables();
            buttonColor = (String) variables.getOrDefault("button_color", "#1890ff");
            buttonText = (String) variables.getOrDefault("button_text", "立即购买");
            variantName = experimentConfig.getVariantName();
        }

        // 应用实验配置
        Button buyButton = findViewById(R.id.buy_button);
        buyButton.setBackgroundColor(Color.parseColor(buttonColor));
        buyButton.setText(buttonText);

        // 记录曝光事件
        trackExposure(variantName);

        // 设置点击事件监听
        buyButton.setOnClickListener(v -> {
            trackButtonClick(variantName);
            handlePurchase();
        });
    }

    private void trackExposure(String variantName) {
        Map<String, Object> exposureProperties = new HashMap<>();
        exposureProperties.put("page", "product_detail");
        exposureProperties.put("product_id", getIntent().getStringExtra("product_id"));
        exposureProperties.put("variant", variantName);

        DataTester.trackExposure(experimentKey, userId, exposureProperties);
    }

    private void trackButtonClick(String variantName) {
        Map<String, Object> clickProperties = new HashMap<>();
        clickProperties.put("experiment", experimentKey);
        clickProperties.put("variant", variantName);
        clickProperties.put("user_id", userId);
        clickProperties.put("product_id", getIntent().getStringExtra("product_id"));
        clickProperties.put("timestamp", System.currentTimeMillis());

        DataTester.trackEvent("button_click", clickProperties);
    }

    private void handlePurchase() {
        // 处理购买逻辑
        Intent intent = new Intent(this, CheckoutActivity.class);
        intent.putExtra("experiment", experimentKey);
        startActivity(intent);
    }
}
```

### 3.2 实验类型

#### UI/UX实验
```javascript
// 页面布局实验
const layoutType = DataTester.getExperimentVariableValue(
  'page_layout_test',
  'grid',
  userId
);

switch(layoutType) {
  case 'grid':
    renderGridLayout();
    break;
  case 'list':
    renderListLayout();
    break;
  case 'card':
    renderCardLayout();
    break;
}
```

#### 算法实验
```javascript
// 推荐算法实验
const algorithmType = DataTester.getExperimentVariableValue(
  'recommendation_algorithm',
  'collaborative_filtering',
  userId
);

const recommendations = getRecommendations(userId, algorithmType);
displayRecommendations(recommendations);
```

#### 定价实验
```javascript
// 定价策略实验
const pricingStrategy = DataTester.getExperimentVariableValue(
  'pricing_test',
  'standard',
  userId
);

const price = calculatePrice(product, pricingStrategy);
displayPrice(price);
```

### 3.3 事件追踪

#### 关键事件定义
```javascript
// 定义事件类型
const EventTypes = {
  EXPOSURE: 'experiment_exposure',
  CLICK: 'button_click',
  CONVERSION: 'purchase_completed',
  ENGAGEMENT: 'page_view_duration'
};

// 追踪曝光
function trackExposure(experimentKey, userId, variant) {
  DataTester.trackEvent(EventTypes.EXPOSURE, {
    experiment_key: experimentKey,
    user_id: userId,
    variant: variant,
    timestamp: Date.now()
  });
}

// 追踪转化
function trackConversion(experimentKey, userId, revenue) {
  DataTester.trackEvent(EventTypes.CONVERSION, {
    experiment_key: experimentKey,
    user_id: userId,
    revenue: revenue,
    timestamp: Date.now()
  });
}
```

## 高级功能

### 4.1 多变量测试

```javascript
// 同时测试多个变量
const multiVariateConfig = DataTester.getExperimentConfig(
  'multi_variate_test',
  {
    button_color: '#1890ff',
    button_text: '立即购买',
    button_size: 'medium'
  },
  userId
);

// 应用多个变量
applyButtonStyle({
  color: multiVariateConfig.button_color,
  text: multiVariateConfig.button_text,
  size: multiVariateConfig.button_size
});
```

### 4.2 互斥实验

```javascript
// 设置实验互斥组
const mutexGroup = 'checkout_flow_tests';

// 只会命中一个实验
const activeExperiment = DataTester.getActiveExperiment(mutexGroup, userId);

switch(activeExperiment) {
  case 'one_step_checkout':
    renderOneStepCheckout();
    break;
  case 'multi_step_checkout':
    renderMultiStepCheckout();
    break;
  default:
    renderDefaultCheckout();
}
```

### 4.3 条件实验

```javascript
// 基于用户属性的条件实验
const userSegment = getUserSegment(userId);

if (userSegment === 'premium_users') {
  const premiumFeature = DataTester.getExperimentVariableValue(
    'premium_feature_test',
    false,
    userId
  );
  
  if (premiumFeature) {
    enablePremiumFeature();
  }
}
```

## 监控与分析

### 5.1 实时监控

```javascript
// 监控实验健康度
function monitorExperimentHealth(experimentKey) {
  const metrics = DataTester.getExperimentMetrics(experimentKey);
  
  // 检查样本量
  if (metrics.sample_size < 1000) {
    console.warn('样本量不足，建议延长实验时间');
  }
  
  // 检查转化率异常
  if (metrics.conversion_rate_change > 0.5) {
    console.warn('转化率变化异常，建议检查实验配置');
  }
  
  // 检查错误率
  if (metrics.error_rate > 0.01) {
    console.error('错误率过高，建议暂停实验');
  }
}
```

### 5.2 数据分析

```javascript
// 获取实验报告
const report = DataTester.getExperimentReport('button_color_test');

console.log('实验结果:', {
  实验名称: report.experiment_name,
  运行时间: report.duration_days,
  样本量: report.total_users,
  统计显著性: report.statistical_significance,
  置信区间: report.confidence_interval,
  主要指标提升: report.primary_metric_lift,
  建议: report.recommendation
});
```

## 故障排除

### 6.1 常见问题

#### SDK初始化失败
```javascript
// 检查初始化状态
if (!DataTester.isInitialized()) {
  console.error('SDK未正确初始化');
  
  // 重新初始化
  DataTester.init({
    app_key: 'your_app_key',
    debug: true // 开启调试模式
  });
}
```

#### 配置获取失败
```javascript
// 添加错误处理
try {
  const config = DataTester.getFeatureFlag('feature_key', defaultValue, userId);
  return config;
} catch (error) {
  console.error('获取配置失败:', error);
  return defaultValue; // 返回默认值
}
```

#### 网络问题
```javascript
// 设置超时和重试
DataTester.configure({
  timeout: 5000, // 5秒超时
  retry_count: 3, // 重试3次
  cache_enabled: true // 启用缓存
});
```

### 6.2 调试技巧

#### 开启调试模式
```javascript
// 开启详细日志
DataTester.setLogLevel('DEBUG');

// 查看实验分配详情
const debugInfo = DataTester.getDebugInfo(userId);
console.log('用户实验分配:', debugInfo);
```

#### 本地测试
```javascript
// 强制指定实验组（仅用于测试）
DataTester.forceVariant('experiment_key', 'treatment', userId);

// 清除本地缓存
DataTester.clearCache();
```

## 最佳实践总结

1. **渐进式发布**：从小流量开始，逐步扩大范围
2. **监控关键指标**：设置告警，及时发现问题
3. **A/A测试验证**：确保实验系统正常工作
4. **统计显著性**：确保足够的样本量和运行时间
5. **业务理解**：结合业务场景解读实验结果
6. **文档记录**：详细记录实验设计和结果
7. **团队协作**：建立实验评审和决策流程
