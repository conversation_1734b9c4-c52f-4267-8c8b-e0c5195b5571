# 火山引擎云控下发和实验下发使用指南

## 目录
1. [快速开始](#快速开始)
2. [FeatureFlag使用指南](#featureflag使用指南)
3. [A/B测试使用指南](#ab测试使用指南)
4. [高级功能](#高级功能)
5. [监控与分析](#监控与分析)
6. [故障排除](#故障排除)

## 快速开始

### 第一步：账号准备
1. 注册火山引擎账号
2. 完成实名认证
3. 开通A/B测试服务
4. 获取AppKey和AccessKey

### 第二步：创建应用
```bash
# 在控制台创建应用
应用名称: MyApp
应用类型: Android/iOS/Web
包名/Bundle ID: com.example.myapp
```

### 第三步：Android SDK集成
```gradle
// app/build.gradle
dependencies {
    implementation 'com.volcengine:datatester-android-sdk:2.1.5'
}
```

```java
// Application类中初始化
public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();

        DataTesterConfig config = new DataTesterConfig.Builder()
            .setAppKey("your_app_key")
            .setChannel(BuildConfig.DEBUG ? "debug" : "release")
            .setDebugMode(BuildConfig.DEBUG)
            .build();

        DataTester.init(this, config);
    }
}
```

## FeatureFlag使用指南

### 2.1 创建Feature

#### 控制台操作
1. 登录火山引擎控制台
2. 进入 **A/B测试** > **FeatureFlag**
3. 点击 **创建Feature**

#### 配置示例
```json
{
  "feature_key": "new_ui_theme",
  "feature_name": "新UI主题",
  "description": "新版本UI主题切换",
  "type": "boolean",
  "default_value": false,
  "target_audience": {
    "percentage": 10,
    "user_groups": ["beta_users"]
  }
}
```

### 2.2 配置类型

#### Boolean类型（Android示例）
```java
// 获取布尔值配置
boolean isNewUIEnabled = DataTester.getFeatureFlag(
    "new_ui_theme",
    false, // 默认值
    userId
);

if (isNewUIEnabled) {
    // 启用新UI
    enableNewUI();
} else {
    // 使用旧UI
    useOldUI();
}

// 在Activity中的实际应用
public class MainActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 根据Feature Flag决定使用哪个布局
        boolean useNewLayout = DataTester.getFeatureFlag(
            "new_main_layout",
            false,
            getCurrentUserId()
        );

        if (useNewLayout) {
            setContentView(R.layout.activity_main_new);
        } else {
            setContentView(R.layout.activity_main_old);
        }

        setupUI();
    }
}
```

#### String类型（Android示例）
```java
// 获取字符串配置
String themeColor = DataTester.getFeatureFlag(
    "theme_color",
    "#1890ff", // 默认值
    userId
);

// 应用主题色到Android视图
View mainContainer = findViewById(R.id.main_container);
mainContainer.setBackgroundColor(Color.parseColor(themeColor));

// 或者应用到ActionBar
if (getSupportActionBar() != null) {
    getSupportActionBar().setBackgroundDrawable(
        new ColorDrawable(Color.parseColor(themeColor))
    );
}
```

#### Number类型（Android示例）
```java
// 获取数值配置
int maxRetryCount = DataTester.getFeatureFlag(
    "max_retry_count",
    3, // 默认值
    userId
);

// 在网络请求中使用重试次数
private void performNetworkRequest() {
    RetrofitClient.getInstance()
        .getApiService()
        .getData()
        .retryWhen(errors -> errors
            .zipWith(Observable.range(1, maxRetryCount), (error, retryCount) -> {
                if (retryCount < maxRetryCount) {
                    return Observable.timer(retryCount * 1000, TimeUnit.MILLISECONDS);
                } else {
                    return Observable.error(error);
                }
            })
            .flatMap(timer -> timer)
        )
        .subscribe(this::handleSuccess, this::handleError);
}
```

#### JSON类型（Android示例）
```java
// 获取JSON配置
String uiConfigJson = DataTester.getFeatureFlag(
    "ui_config",
    "{\"buttonSize\":\"medium\",\"showAnimation\":true}", // 默认JSON字符串
    userId
);

// 解析JSON配置
try {
    JSONObject uiConfig = new JSONObject(uiConfigJson);
    String buttonSize = uiConfig.getString("buttonSize");
    boolean showAnimation = uiConfig.getBoolean("showAnimation");

    // 应用UI配置
    applyUIConfig(buttonSize, showAnimation);

} catch (JSONException e) {
    Log.e("FeatureFlag", "解析UI配置失败", e);
    // 使用默认配置
    applyUIConfig("medium", true);
}

private void applyUIConfig(String buttonSize, boolean showAnimation) {
    Button actionButton = findViewById(R.id.action_button);

    // 设置按钮大小
    ViewGroup.LayoutParams params = actionButton.getLayoutParams();
    switch (buttonSize) {
        case "small":
            params.height = dpToPx(32);
            break;
        case "large":
            params.height = dpToPx(56);
            break;
        default: // medium
            params.height = dpToPx(44);
            break;
    }
    actionButton.setLayoutParams(params);

    // 设置动画
    if (showAnimation) {
        actionButton.setOnClickListener(v -> {
            v.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction(() -> {
                    v.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start();
                })
                .start();
        });
    }
}

private int dpToPx(int dp) {
    return (int) (dp * getResources().getDisplayMetrics().density);
}
```

### 2.3 人群定向和用户组管理

#### 用户ID规则和要求

**用户ID格式要求**
```java
// Android中设置用户ID
public class UserIdManager {

    // 推荐的用户ID格式
    public static String generateUserId() {
        // 方式1: 使用业务用户ID（推荐）
        String businessUserId = "user_12345678";

        // 方式2: 使用UUID
        String uuidUserId = UUID.randomUUID().toString();

        // 方式3: 组合格式（平台_业务ID）
        String combinedUserId = "android_user_12345678";

        return businessUserId; // 选择一种格式
    }

    // 用户ID验证规则
    public static boolean isValidUserId(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }

        // 长度限制：1-256个字符
        if (userId.length() > 256) {
            return false;
        }

        // 字符限制：字母、数字、下划线、连字符
        return userId.matches("^[a-zA-Z0-9_-]+$");
    }
}
```

**用户ID最佳实践**
- ✅ 使用业务系统中的唯一用户标识
- ✅ 保持用户ID在整个用户生命周期中不变
- ✅ 长度控制在1-256个字符内
- ✅ 只使用字母、数字、下划线、连字符
- ❌ 避免使用设备ID（IMEI、MAC地址等）
- ❌ 避免使用可能变化的标识（手机号、邮箱等）

#### 用户组配置详解

**1. 百分比分流**
```json
{
  "feature_key": "new_ui_theme",
  "target_audience": {
    "percentage": 10,  // 10%的用户
    "distribution_method": "hash_based"  // 基于用户ID哈希分流
  }
}
```

**2. 用户组定向**
```json
{
  "feature_key": "beta_feature",
  "target_audience": {
    "user_groups": ["beta_users", "internal_users", "vip_users"],
    "group_logic": "OR"  // 满足任一用户组即可
  }
}
```

**3. 组合条件**
```json
{
  "feature_key": "premium_feature",
  "target_audience": {
    "percentage": 20,
    "user_groups": ["premium_users"],
    "user_attributes": {
      "user_level": {">=": 5},
      "city": {"in": ["北京", "上海", "深圳"]},
      "registration_days": {">=": 30}
    },
    "logic": "AND"  // 所有条件都要满足
  }
}
```

#### Android中的用户组管理

**设置用户属性和分组**
```java
public class UserGroupManager {
    private DataTesterService dataTesterService;

    public UserGroupManager() {
        this.dataTesterService = DataTesterService.getInstance();
    }

    // 设置用户基础信息和分组
    public void setupUserProfile(String userId, UserProfile profile) {
        Map<String, Object> userAttributes = new HashMap<>();

        // 基础属性
        userAttributes.put("user_level", profile.getUserLevel());
        userAttributes.put("city", profile.getCity());
        userAttributes.put("age", profile.getAge());
        userAttributes.put("gender", profile.getGender());
        userAttributes.put("registration_date", profile.getRegistrationDate());

        // 计算注册天数
        long registrationDays = calculateRegistrationDays(profile.getRegistrationDate());
        userAttributes.put("registration_days", registrationDays);

        // 设备信息
        userAttributes.put("device_type", "Android");
        userAttributes.put("app_version", BuildConfig.VERSION_NAME);
        userAttributes.put("os_version", Build.VERSION.RELEASE);

        // 业务分组
        List<String> userGroups = determineUserGroups(profile);
        userAttributes.put("user_groups", userGroups);

        // 设置到DataTester
        dataTesterService.setUser(userId, userAttributes);

        Log.d("UserGroup", "用户分组设置完成: " + userGroups);
    }

    // 确定用户所属分组
    private List<String> determineUserGroups(UserProfile profile) {
        List<String> groups = new ArrayList<>();

        // VIP用户分组
        if (profile.getUserLevel() >= 8) {
            groups.add("vip_users");
        }

        // 高级用户分组
        if (profile.getUserLevel() >= 5) {
            groups.add("premium_users");
        }

        // Beta测试用户分组
        if (profile.isBetaTester()) {
            groups.add("beta_users");
        }

        // 内部用户分组
        if (profile.isInternalUser()) {
            groups.add("internal_users");
        }

        // 活跃用户分组（最近30天有活动）
        if (profile.getLastActiveDate() != null &&
            isWithinDays(profile.getLastActiveDate(), 30)) {
            groups.add("active_users");
        }

        // 新用户分组（注册不超过7天）
        long registrationDays = calculateRegistrationDays(profile.getRegistrationDate());
        if (registrationDays <= 7) {
            groups.add("new_users");
        }

        // 地域分组
        if (Arrays.asList("北京", "上海", "深圳", "广州").contains(profile.getCity())) {
            groups.add("tier1_city_users");
        }

        return groups;
    }

    // 动态更新用户分组
    public void updateUserGroups(String userId) {
        UserProfile profile = UserService.getInstance().getUserProfile(userId);
        if (profile != null) {
            setupUserProfile(userId, profile);
        }
    }

    // 检查用户是否属于特定分组
    public boolean isUserInGroup(String userId, String groupName) {
        UserProfile profile = UserService.getInstance().getUserProfile(userId);
        if (profile == null) return false;

        List<String> userGroups = determineUserGroups(profile);
        return userGroups.contains(groupName);
    }

    private long calculateRegistrationDays(String registrationDate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            Date regDate = sdf.parse(registrationDate);
            Date now = new Date();
            long diffInMillies = now.getTime() - regDate.getTime();
            return TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
        } catch (ParseException e) {
            Log.e("UserGroup", "解析注册日期失败", e);
            return 0;
        }
    }

    private boolean isWithinDays(String dateString, int days) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            Date date = sdf.parse(dateString);
            Date now = new Date();
            long diffInMillies = now.getTime() - date.getTime();
            long diffInDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
            return diffInDays <= days;
        } catch (ParseException e) {
            return false;
        }
    }
}
```

#### 控制台中的用户组配置

**1. 创建用户分组**
```bash
# 在火山引擎控制台操作步骤：
1. 进入 A/B测试 > 用户管理 > 用户分组
2. 点击"创建分组"
3. 填写分组信息：
   - 分组名称: beta_users
   - 分组描述: Beta测试用户
   - 分组条件: user_groups contains "beta_users"
4. 保存分组配置
```

**2. Feature Flag中使用用户分组**
```json
{
  "feature_key": "new_checkout_flow",
  "feature_name": "新购买流程",
  "target_audience": {
    "user_groups": ["beta_users", "internal_users"],
    "percentage": 100,  // 对指定用户组100%开放
    "fallback_percentage": 5  // 对其他用户5%开放
  },
  "value": true
}
```

#### 分流算法说明

**哈希分流算法**
```java
public class DistributionAlgorithm {

    // 火山引擎使用的分流算法示例
    public static boolean shouldUserReceiveFeature(String userId, String featureKey, int percentage) {
        // 组合用户ID和特征键
        String hashInput = userId + "_" + featureKey;

        // 计算哈希值
        int hashValue = Math.abs(hashInput.hashCode());

        // 取模得到0-99的值
        int bucket = hashValue % 100;

        // 判断是否在目标百分比内
        return bucket < percentage;
    }

    // 验证分流一致性
    public static void testDistributionConsistency() {
        String userId = "user_12345";
        String featureKey = "new_feature";
        int percentage = 10;

        // 多次调用应该返回相同结果
        boolean result1 = shouldUserReceiveFeature(userId, featureKey, percentage);
        boolean result2 = shouldUserReceiveFeature(userId, featureKey, percentage);

        assert result1 == result2 : "分流结果应该保持一致";

        Log.d("Distribution", "用户 " + userId + " 分流结果: " + result1);
    }
}
```

#### 实际应用场景示例

**场景1: 新功能Beta测试**
```java
// 1. 在Activity中检查用户是否为Beta用户
public class MainActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 设置用户信息
        setupUserProfile();

        // 检查Beta功能
        checkBetaFeatures();
    }

    private void setupUserProfile() {
        String userId = UserManager.getInstance().getCurrentUserId();
        UserProfile profile = UserManager.getInstance().getUserProfile();

        // 设置用户分组
        UserGroupManager groupManager = new UserGroupManager();
        groupManager.setupUserProfile(userId, profile);
    }

    private void checkBetaFeatures() {
        String userId = UserManager.getInstance().getCurrentUserId();

        // 检查新UI主题（只对Beta用户开放）
        boolean newUIEnabled = DataTesterService.getInstance().getFeatureFlag(
            "new_ui_theme_beta",
            false
        );

        if (newUIEnabled) {
            Log.d("Beta", "用户 " + userId + " 启用Beta UI主题");
            applyBetaTheme();
        }

        // 检查高级搜索功能（对Premium用户和Beta用户开放）
        boolean advancedSearchEnabled = DataTesterService.getInstance().getFeatureFlag(
            "advanced_search",
            false
        );

        if (advancedSearchEnabled) {
            enableAdvancedSearch();
        }
    }
}
```

**场景2: 地域定向功能**
```java
// 根据用户城市开放不同功能
public class LocationBasedFeatures {

    public void checkLocationFeatures(String userId, String userCity) {
        DataTesterService service = DataTesterService.getInstance();

        // 同城配送功能（仅一线城市）
        boolean sameCityDelivery = service.getFeatureFlag("same_city_delivery", false);
        if (sameCityDelivery && isTier1City(userCity)) {
            enableSameCityDelivery();
        }

        // 本地生活服务（特定城市）
        boolean localServices = service.getFeatureFlag("local_services", false);
        if (localServices) {
            loadLocalServices(userCity);
        }
    }

    private boolean isTier1City(String city) {
        return Arrays.asList("北京", "上海", "深圳", "广州").contains(city);
    }
}
```

**场景3: 用户等级分层功能**
```java
// 根据用户等级开放不同功能
public class UserLevelFeatures {

    public void setupLevelBasedFeatures(String userId, int userLevel) {
        DataTesterService service = DataTesterService.getInstance();

        // VIP专享功能（等级8以上）
        if (userLevel >= 8) {
            boolean vipFeatures = service.getFeatureFlag("vip_exclusive_features", false);
            if (vipFeatures) {
                enableVIPFeatures();
            }
        }

        // 高级用户功能（等级5以上）
        if (userLevel >= 5) {
            boolean premiumFeatures = service.getFeatureFlag("premium_features", false);
            if (premiumFeatures) {
                enablePremiumFeatures();
            }
        }

        // 新手引导（等级3以下）
        if (userLevel <= 3) {
            boolean newUserGuide = service.getFeatureFlag("new_user_guide", true);
            if (newUserGuide) {
                showNewUserGuide();
            }
        }
    }
}
```

#### 常见问题和解决方案

**Q1: 用户分组不生效怎么办？**
```java
// 调试用户分组问题
public class UserGroupDebugger {

    public void debugUserGroups(String userId) {
        DataTesterService service = DataTesterService.getInstance();

        // 1. 检查用户ID是否有效
        if (!UserIdManager.isValidUserId(userId)) {
            Log.e("Debug", "用户ID格式无效: " + userId);
            return;
        }

        // 2. 检查用户属性是否正确设置
        UserProfile profile = UserService.getInstance().getUserProfile(userId);
        if (profile == null) {
            Log.e("Debug", "用户资料不存在: " + userId);
            return;
        }

        // 3. 检查分组逻辑
        UserGroupManager groupManager = new UserGroupManager();
        List<String> userGroups = groupManager.determineUserGroups(profile);
        Log.d("Debug", "用户分组: " + userGroups);

        // 4. 检查Feature Flag配置
        boolean featureEnabled = service.getFeatureFlag("test_feature", false);
        Log.d("Debug", "Feature Flag结果: " + featureEnabled);

        // 5. 强制刷新配置（调试用）
        if (BuildConfig.DEBUG) {
            service.refreshConfigs();
        }
    }
}
```

**Q2: 分流结果不一致怎么办？**
```java
// 确保分流一致性
public class ConsistencyChecker {

    public void checkDistributionConsistency(String userId, String featureKey) {
        DataTesterService service = DataTesterService.getInstance();

        // 多次获取同一Feature Flag，结果应该一致
        List<Boolean> results = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            boolean result = service.getFeatureFlag(featureKey, false);
            results.add(result);
        }

        // 检查一致性
        boolean firstResult = results.get(0);
        boolean isConsistent = results.stream().allMatch(r -> r.equals(firstResult));

        if (!isConsistent) {
            Log.e("Consistency", "分流结果不一致！用户: " + userId + ", 特征: " + featureKey);
            Log.e("Consistency", "结果列表: " + results);
        } else {
            Log.d("Consistency", "分流结果一致: " + firstResult);
        }
    }
}
```

**Q3: 如何实现渐进式发布？**
```java
// 渐进式发布管理
public class GradualRolloutManager {

    private static final String ROLLOUT_PERCENTAGE_KEY = "rollout_percentage_";

    public void manageGradualRollout(String featureKey) {
        DataTesterService service = DataTesterService.getInstance();

        // 获取当前发布百分比
        int currentPercentage = service.getFeatureFlag(
            ROLLOUT_PERCENTAGE_KEY + featureKey,
            0
        );

        Log.d("Rollout", "功能 " + featureKey + " 当前发布比例: " + currentPercentage + "%");

        // 根据发布阶段显示不同提示
        if (currentPercentage == 0) {
            Log.d("Rollout", "功能未发布");
        } else if (currentPercentage < 10) {
            Log.d("Rollout", "小范围测试阶段");
            enableDetailedLogging();
        } else if (currentPercentage < 50) {
            Log.d("Rollout", "扩大测试阶段");
            enableMonitoring();
        } else if (currentPercentage < 100) {
            Log.d("Rollout", "大规模发布阶段");
        } else {
            Log.d("Rollout", "全量发布");
        }
    }

    private void enableDetailedLogging() {
        // 在小范围测试时启用详细日志
        DataTesterService.getInstance().setLogLevel("DEBUG");
    }

    private void enableMonitoring() {
        // 在扩大测试时启用监控
        // 监控关键指标：错误率、性能、用户反馈等
    }
}
```

#### 最佳实践总结

**用户分组管理最佳实践**
1. **明确分组标准**: 基于业务逻辑定义清晰的分组规则
2. **动态更新**: 用户属性变化时及时更新分组
3. **分组验证**: 定期验证分组逻辑的正确性
4. **文档记录**: 详细记录每个分组的定义和用途
5. **权限控制**: 敏感分组（如内部用户）需要严格权限控制

**分流策略最佳实践**
1. **渐进式发布**: 从小比例开始，逐步扩大
2. **监控指标**: 密切关注关键业务指标
3. **快速回滚**: 准备应急回滚方案
4. **A/A测试**: 定期进行A/A测试验证系统正确性
5. **用户体验**: 确保分流不影响用户体验的一致性
```

### 2.4 灰度发布

#### 阶段性发布
```bash
阶段1: 5%用户  -> 观察1天
阶段2: 20%用户 -> 观察1天  
阶段3: 50%用户 -> 观察1天
阶段4: 100%用户 -> 全量发布
```

#### 代码示例
```javascript
// 检查Feature状态
const featureStatus = DataTester.getFeatureStatus('new_feature');

switch(featureStatus.stage) {
  case 'testing':
    // 测试阶段，记录详细日志
    enableDetailedLogging();
    break;
  case 'rolling_out':
    // 灰度阶段，监控关键指标
    monitorKeyMetrics();
    break;
  case 'fully_released':
    // 全量发布，正常运行
    break;
}
```

## A/B测试使用指南

### 3.1 创建实验

#### 实验设计
```json
{
  "experiment_name": "购买按钮颜色测试",
  "hypothesis": "红色按钮比蓝色按钮有更高的点击率",
  "primary_metric": "button_click_rate",
  "secondary_metrics": ["conversion_rate", "revenue_per_user"],
  "traffic_allocation": 20,
  "variants": [
    {
      "name": "control",
      "allocation": 50,
      "config": {"button_color": "#1890ff"}
    },
    {
      "name": "treatment",
      "allocation": 50,
      "config": {"button_color": "#ff4d4f"}
    }
  ]
}
```

#### Android代码实现
```java
public class ProductDetailActivity extends AppCompatActivity {
    private String userId;
    private String experimentKey = "button_color_test";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_product_detail);

        userId = UserManager.getInstance().getCurrentUserId();
        setupExperiment();
    }

    private void setupExperiment() {
        // 获取实验配置
        Map<String, Object> defaultConfig = new HashMap<>();
        defaultConfig.put("button_color", "#1890ff");
        defaultConfig.put("button_text", "立即购买");

        ExperimentConfig experimentConfig = DataTester.getExperimentConfig(
            experimentKey,
            userId
        );

        String buttonColor = "#1890ff"; // 默认值
        String buttonText = "立即购买"; // 默认值
        String variantName = "control"; // 默认分组

        if (experimentConfig != null) {
            Map<String, Object> variables = experimentConfig.getVariables();
            buttonColor = (String) variables.getOrDefault("button_color", "#1890ff");
            buttonText = (String) variables.getOrDefault("button_text", "立即购买");
            variantName = experimentConfig.getVariantName();
        }

        // 应用实验配置
        Button buyButton = findViewById(R.id.buy_button);
        buyButton.setBackgroundColor(Color.parseColor(buttonColor));
        buyButton.setText(buttonText);

        // 记录曝光事件
        trackExposure(variantName);

        // 设置点击事件监听
        buyButton.setOnClickListener(v -> {
            trackButtonClick(variantName);
            handlePurchase();
        });
    }

    private void trackExposure(String variantName) {
        Map<String, Object> exposureProperties = new HashMap<>();
        exposureProperties.put("page", "product_detail");
        exposureProperties.put("product_id", getIntent().getStringExtra("product_id"));
        exposureProperties.put("variant", variantName);

        DataTester.trackExposure(experimentKey, userId, exposureProperties);
    }

    private void trackButtonClick(String variantName) {
        Map<String, Object> clickProperties = new HashMap<>();
        clickProperties.put("experiment", experimentKey);
        clickProperties.put("variant", variantName);
        clickProperties.put("user_id", userId);
        clickProperties.put("product_id", getIntent().getStringExtra("product_id"));
        clickProperties.put("timestamp", System.currentTimeMillis());

        DataTester.trackEvent("button_click", clickProperties);
    }

    private void handlePurchase() {
        // 处理购买逻辑
        Intent intent = new Intent(this, CheckoutActivity.class);
        intent.putExtra("experiment", experimentKey);
        startActivity(intent);
    }
}
```

### 3.2 Android实验类型

#### UI/UX实验
```java
// 页面布局实验
public class LayoutExperimentManager {

    public void applyLayoutExperiment(Activity activity, String userId) {
        String layoutType = DataTester.getExperimentVariableValue(
            "page_layout_test",
            "grid",
            userId
        );

        switch(layoutType) {
            case "grid":
                activity.setContentView(R.layout.activity_main_grid);
                break;
            case "list":
                activity.setContentView(R.layout.activity_main_list);
                break;
            case "card":
                activity.setContentView(R.layout.activity_main_card);
                break;
            default:
                activity.setContentView(R.layout.activity_main_grid);
                break;
        }

        // 追踪实验曝光
        DataTester.trackExposure("page_layout_test", userId, null);
    }
}
```

#### 算法实验
```java
// 推荐算法实验
public class RecommendationExperiment {

    public List<Product> getRecommendations(String userId, String productId) {
        String algorithmType = DataTester.getExperimentVariableValue(
            "recommendation_algorithm",
            "collaborative_filtering",
            userId
        );

        RecommendationService service = RecommendationService.getInstance();
        List<Product> recommendations;

        switch(algorithmType) {
            case "content_based":
                recommendations = service.getContentBasedRecommendations(userId, productId);
                break;
            case "popularity_based":
                recommendations = service.getPopularityBasedRecommendations(userId);
                break;
            default: // collaborative_filtering
                recommendations = service.getCollaborativeFilteringRecommendations(userId);
                break;
        }

        // 追踪推荐算法实验
        Map<String, Object> properties = new HashMap<>();
        properties.put("algorithm", algorithmType);
        properties.put("product_id", productId);
        properties.put("recommendation_count", recommendations.size());

        DataTester.trackEvent("recommendation_shown", properties);

        return recommendations;
    }
}
```

#### 定价实验
```java
// 定价策略实验
public class PricingExperiment {

    public double calculateExperimentPrice(Product product, String userId) {
        String pricingStrategy = DataTester.getExperimentVariableValue(
            "pricing_test",
            "standard",
            userId
        );

        double finalPrice;

        switch(pricingStrategy) {
            case "premium":
                finalPrice = product.getPrice() * 1.2; // 提价20%
                break;
            case "discount":
                finalPrice = product.getPrice() * 0.9; // 降价10%
                break;
            case "dynamic":
                finalPrice = calculateDynamicPrice(product, userId);
                break;
            default: // standard
                finalPrice = product.getPrice();
                break;
        }

        // 追踪定价实验
        Map<String, Object> properties = new HashMap<>();
        properties.put("product_id", product.getId());
        properties.put("pricing_strategy", pricingStrategy);
        properties.put("original_price", product.getPrice());
        properties.put("final_price", finalPrice);

        DataTester.trackEvent("pricing_experiment", properties);

        return finalPrice;
    }

    private double calculateDynamicPrice(Product product, String userId) {
        // 动态定价逻辑
        UserProfile profile = UserService.getInstance().getUserProfile(userId);
        if (profile != null && profile.getUserLevel() >= 5) {
            return product.getPrice() * 0.95; // VIP用户95折
        }
        return product.getPrice();
    }
}
```

### 3.3 Android事件追踪

#### 关键事件定义
```java
// 定义事件类型常量
public class EventTypes {
    public static final String EXPOSURE = "experiment_exposure";
    public static final String CLICK = "button_click";
    public static final String CONVERSION = "purchase_completed";
    public static final String ENGAGEMENT = "page_view_duration";
    public static final String PRODUCT_VIEW = "product_view";
    public static final String ADD_TO_CART = "add_to_cart";
}

// 事件追踪管理器
public class ExperimentTracker {
    private DataTesterService dataTesterService;

    public ExperimentTracker() {
        this.dataTesterService = DataTesterService.getInstance();
    }

    // 追踪实验曝光
    public void trackExposure(String experimentKey, String userId, String variant) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("experiment_key", experimentKey);
        properties.put("user_id", userId);
        properties.put("variant", variant);
        properties.put("timestamp", System.currentTimeMillis());

        dataTesterService.trackEvent(EventTypes.EXPOSURE, properties);
    }

    // 追踪转化事件
    public void trackConversion(String experimentKey, String userId, double revenue) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("experiment_key", experimentKey);
        properties.put("user_id", userId);
        properties.put("revenue", revenue);
        properties.put("timestamp", System.currentTimeMillis());

        dataTesterService.trackEvent(EventTypes.CONVERSION, properties);
    }

    // 追踪按钮点击
    public void trackButtonClick(String experimentKey, String buttonId, String variant) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("experiment_key", experimentKey);
        properties.put("button_id", buttonId);
        properties.put("variant", variant);
        properties.put("timestamp", System.currentTimeMillis());

        dataTesterService.trackEvent(EventTypes.CLICK, properties);
    }

    // 追踪页面停留时间
    public void trackPageEngagement(String pageName, long durationMs) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("page_name", pageName);
        properties.put("duration_ms", durationMs);
        properties.put("timestamp", System.currentTimeMillis());

        dataTesterService.trackEvent(EventTypes.ENGAGEMENT, properties);
    }
}
```

## 高级功能

### 4.1 多变量测试

```javascript
// 同时测试多个变量
const multiVariateConfig = DataTester.getExperimentConfig(
  'multi_variate_test',
  {
    button_color: '#1890ff',
    button_text: '立即购买',
    button_size: 'medium'
  },
  userId
);

// 应用多个变量
applyButtonStyle({
  color: multiVariateConfig.button_color,
  text: multiVariateConfig.button_text,
  size: multiVariateConfig.button_size
});
```

### 4.2 互斥实验

```javascript
// 设置实验互斥组
const mutexGroup = 'checkout_flow_tests';

// 只会命中一个实验
const activeExperiment = DataTester.getActiveExperiment(mutexGroup, userId);

switch(activeExperiment) {
  case 'one_step_checkout':
    renderOneStepCheckout();
    break;
  case 'multi_step_checkout':
    renderMultiStepCheckout();
    break;
  default:
    renderDefaultCheckout();
}
```

### 4.3 条件实验

```javascript
// 基于用户属性的条件实验
const userSegment = getUserSegment(userId);

if (userSegment === 'premium_users') {
  const premiumFeature = DataTester.getExperimentVariableValue(
    'premium_feature_test',
    false,
    userId
  );
  
  if (premiumFeature) {
    enablePremiumFeature();
  }
}
```

## 监控与分析

### 5.1 实时监控

```javascript
// 监控实验健康度
function monitorExperimentHealth(experimentKey) {
  const metrics = DataTester.getExperimentMetrics(experimentKey);
  
  // 检查样本量
  if (metrics.sample_size < 1000) {
    console.warn('样本量不足，建议延长实验时间');
  }
  
  // 检查转化率异常
  if (metrics.conversion_rate_change > 0.5) {
    console.warn('转化率变化异常，建议检查实验配置');
  }
  
  // 检查错误率
  if (metrics.error_rate > 0.01) {
    console.error('错误率过高，建议暂停实验');
  }
}
```

### 5.2 数据分析

```javascript
// 获取实验报告
const report = DataTester.getExperimentReport('button_color_test');

console.log('实验结果:', {
  实验名称: report.experiment_name,
  运行时间: report.duration_days,
  样本量: report.total_users,
  统计显著性: report.statistical_significance,
  置信区间: report.confidence_interval,
  主要指标提升: report.primary_metric_lift,
  建议: report.recommendation
});
```

## 故障排除

### 6.1 Android常见问题

#### SDK初始化失败
```java
// 检查初始化状态
public class SDKDiagnostics {

    public void checkSDKStatus() {
        DataTesterService service = DataTesterService.getInstance();

        if (!service.isInitialized()) {
            Log.e("SDK", "SDK未正确初始化");

            // 重新初始化
            DataTesterConfig config = new DataTesterConfig.Builder()
                .setAppKey(BuildConfig.DATATESTER_APP_KEY)
                .setDebugMode(true) // 开启调试模式
                .build();

            DataTester.init(getApplicationContext(), config);
        }
    }
}
```

#### 配置获取失败
```java
// 添加错误处理
public class ConfigHelper {

    public <T> T getFeatureFlagSafely(String featureKey, T defaultValue, String userId) {
        try {
            DataTesterService service = DataTesterService.getInstance();
            return service.getFeatureFlag(featureKey, defaultValue);
        } catch (Exception e) {
            Log.e("Config", "获取配置失败: " + featureKey, e);
            return defaultValue; // 返回默认值
        }
    }
}
```

#### 网络问题
```java
// 设置超时和重试
public void configureNetworkSettings() {
    DataTesterConfig config = new DataTesterConfig.Builder()
        .setAppKey(BuildConfig.DATATESTER_APP_KEY)
        .setNetworkTimeout(5000) // 5秒超时
        .setCacheEnabled(true) // 启用缓存
        .build();

    DataTester.init(getApplicationContext(), config);
}
```

### 6.2 Android调试技巧

#### 开启调试模式
```java
// 开启详细日志
public class DebugHelper {

    public void enableDebugMode() {
        if (BuildConfig.DEBUG) {
            // 设置日志级别
            DataTester.setLogLevel(LogLevel.DEBUG);

            // 查看实验分配详情
            String userId = UserManager.getInstance().getCurrentUserId();
            if (userId != null) {
                Map<String, Object> debugInfo = DataTester.getDebugInfo(userId);
                Log.d("Debug", "用户实验分配: " + debugInfo);
            }
        }
    }
}
```

#### 本地测试
```java
// 强制指定实验组（仅用于测试）
public class TestHelper {

    public void forceExperimentVariant(String experimentKey, String variant, String userId) {
        if (BuildConfig.DEBUG) {
            DataTester.forceVariant(experimentKey, variant, userId);
            Log.d("Test", "强制设置实验组: " + experimentKey + " -> " + variant);
        }
    }

    public void clearCache() {
        if (BuildConfig.DEBUG) {
            DataTester.clearCache();
            Log.d("Test", "已清除本地缓存");
        }
    }

    public void resetUserForTesting() {
        if (BuildConfig.DEBUG) {
            DataTester.clearUser();
            DataTester.clearCache();
            Log.d("Test", "已重置用户状态");
        }
    }
}
```

## 最佳实践总结

1. **渐进式发布**：从小流量开始，逐步扩大范围
2. **监控关键指标**：设置告警，及时发现问题
3. **A/A测试验证**：确保实验系统正常工作
4. **统计显著性**：确保足够的样本量和运行时间
5. **业务理解**：结合业务场景解读实验结果
6. **文档记录**：详细记录实验设计和结果
7. **团队协作**：建立实验评审和决策流程
