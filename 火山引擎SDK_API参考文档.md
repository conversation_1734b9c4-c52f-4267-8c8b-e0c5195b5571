# 火山引擎 SDK API 参考文档

## 目录
1. [初始化API](#初始化api)
2. [FeatureFlag API](#featureflag-api)
3. [A/B测试 API](#ab测试-api)
4. [事件追踪 API](#事件追踪-api)
5. [用户管理 API](#用户管理-api)
6. [配置管理 API](#配置管理-api)

## 初始化API

### DataTester.init() - Android
初始化SDK

#### 语法
```java
DataTester.init(Context context, DataTesterConfig config)
DataTester.init(Context context, DataTesterConfig config, InitCallback callback)
```

#### 参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| context | Context | 是 | Android上下文，建议使用Application Context |
| config | DataTesterConfig | 是 | 初始化配置对象 |
| callback | InitCallback | 否 | 初始化回调接口 |

#### DataTesterConfig配置属性
```java
DataTesterConfig config = new DataTesterConfig.Builder()
    .setAppKey(String appKey)              // 应用密钥（必填）
    .setChannel(String channel)            // 渠道标识（可选）
    .setUserId(String userId)              // 用户ID（可选）
    .setDebugMode(boolean debug)           // 调试模式（可选，默认false）
    .setLogLevel(LogLevel level)           // 日志级别（可选）
    .setNetworkTimeout(int timeout)        // 网络超时时间（可选，默认5000ms）
    .setCacheEnabled(boolean enabled)      // 是否启用缓存（可选，默认true）
    .setAutoTrackEnabled(boolean enabled)  // 自动追踪（可选，默认true）
    .setServerUrl(String url)              // 服务器地址（可选）
    .build();
```

#### Android示例
```java
// 在Application类中初始化（推荐）
public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();

        DataTesterConfig config = new DataTesterConfig.Builder()
            .setAppKey("your_app_key_here")
            .setChannel("production")
            .setDebugMode(BuildConfig.DEBUG)
            .setLogLevel(BuildConfig.DEBUG ? LogLevel.DEBUG : LogLevel.ERROR)
            .setNetworkTimeout(10000)
            .setCacheEnabled(true)
            .setAutoTrackEnabled(true)
            .build();

        DataTester.init(this, config, new InitCallback() {
            @Override
            public void onSuccess() {
                Log.d("DataTester", "SDK初始化成功");
            }

            @Override
            public void onFailure(String error) {
                Log.e("DataTester", "SDK初始化失败: " + error);
            }
        });
    }
}
```

#### Web/JS示例
```javascript
DataTester.init({
  app_key: 'your_app_key_here',
  channel: 'official',
  debug: true,
  timeout: 10000,
  cache_enabled: true
});
```

### DataTester.isInitialized()
检查SDK是否已初始化

#### 语法
```javascript
DataTester.isInitialized()
```

#### 返回值
- `boolean` - true表示已初始化，false表示未初始化

## FeatureFlag API

### DataTester.getFeatureFlag() - Android
获取Feature Flag配置值

#### 语法
```java
// 基础方法
<T> T getFeatureFlag(String key, T defaultValue, String userId)
<T> T getFeatureFlag(String key, T defaultValue, String userId, Map<String, Object> userAttributes)

// 异步方法
void getFeatureFlagAsync(String key, Object defaultValue, String userId, FeatureFlagCallback<T> callback)
```

#### 参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| key | String | 是 | Feature Flag的键名 |
| defaultValue | T | 是 | 默认值，支持Boolean、String、Integer、Double、JSONObject等 |
| userId | String | 是 | 用户ID |
| userAttributes | Map<String, Object> | 否 | 用户属性 |
| callback | FeatureFlagCallback<T> | 是 | 异步回调接口 |

#### 返回值
- `T` - Feature Flag的配置值，类型与defaultValue一致

#### Android示例
```java
public class FeatureFlagManager {
    private String userId;

    public FeatureFlagManager(String userId) {
        this.userId = userId;
    }

    // 获取布尔值
    public boolean isNewFeatureEnabled() {
        return DataTester.getFeatureFlag(
            "new_feature_enabled",
            false, // 默认值
            userId
        );
    }

    // 获取字符串值
    public String getAppTheme() {
        Map<String, Object> userAttributes = new HashMap<>();
        userAttributes.put("user_level", "premium");

        return DataTester.getFeatureFlag(
            "app_theme",
            "light", // 默认值
            userId,
            userAttributes
        );
    }

    // 获取数值
    public int getMaxItemsPerPage() {
        return DataTester.getFeatureFlag(
            "max_items_per_page",
            10, // 默认值
            userId
        );
    }

    // 获取JSON配置
    public JSONObject getUIConfig() {
        String defaultConfigJson = "{\"showBanner\":true,\"animationSpeed\":\"normal\"}";
        String configJson = DataTester.getFeatureFlag(
            "ui_config",
            defaultConfigJson,
            userId
        );

        try {
            return new JSONObject(configJson);
        } catch (JSONException e) {
            Log.e("FeatureFlag", "解析UI配置失败", e);
            try {
                return new JSONObject(defaultConfigJson);
            } catch (JSONException ex) {
                return new JSONObject();
            }
        }
    }

    // 异步获取配置（推荐用于网络敏感场景）
    public void getThemeAsync(FeatureFlagCallback<String> callback) {
        DataTester.getFeatureFlagAsync(
            "app_theme",
            "light",
            userId,
            callback
        );
    }
}

// 在Activity中使用
public class MainActivity extends AppCompatActivity {
    private FeatureFlagManager featureFlagManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        String userId = UserManager.getInstance().getCurrentUserId();
        featureFlagManager = new FeatureFlagManager(userId);

        setupUI();
    }

    private void setupUI() {
        // 根据Feature Flag决定UI
        if (featureFlagManager.isNewFeatureEnabled()) {
            setContentView(R.layout.activity_main_new);
        } else {
            setContentView(R.layout.activity_main_old);
        }

        // 异步获取主题配置
        featureFlagManager.getThemeAsync(new FeatureFlagCallback<String>() {
            @Override
            public void onSuccess(String theme) {
                runOnUiThread(() -> applyTheme(theme));
            }

            @Override
            public void onFailure(String error) {
                Log.e("MainActivity", "获取主题配置失败: " + error);
                applyTheme("light"); // 使用默认主题
            }
        });
    }

    private void applyTheme(String theme) {
        // 应用主题逻辑
        if ("dark".equals(theme)) {
            setTheme(R.style.DarkTheme);
        } else {
            setTheme(R.style.LightTheme);
        }
        recreate(); // 重新创建Activity以应用主题
    }
}
```

#### Web/JS示例
```javascript
// 获取布尔值
const isEnabled = DataTester.getFeatureFlag(
  'new_feature_enabled',
  false,
  'user123'
);

// 获取字符串值
const theme = DataTester.getFeatureFlag(
  'app_theme',
  'light',
  'user123',
  { user_level: 'premium' }
);
```

### DataTester.getAllFeatureFlags()
获取用户的所有Feature Flag配置

#### 语法
```javascript
DataTester.getAllFeatureFlags(userId, userAttributes)
```

#### 参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| userId | string | 是 | 用户ID |
| userAttributes | Object | 否 | 用户属性 |

#### 返回值
- `Object` - 包含所有Feature Flag的对象

#### 示例
```javascript
const allFlags = DataTester.getAllFeatureFlags('user123', {
  user_level: 'premium',
  city: 'Beijing'
});

console.log(allFlags);
// 输出: { new_feature_enabled: true, app_theme: 'dark', max_items: 20 }
```

## A/B测试 API

### DataTester.getExperimentVariableValue()
获取实验变量值

#### 语法
```javascript
DataTester.getExperimentVariableValue(experimentKey, variableKey, defaultValue, userId, userAttributes)
```

#### 参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| experimentKey | string | 是 | 实验键名 |
| variableKey | string | 是 | 变量键名 |
| defaultValue | any | 是 | 默认值 |
| userId | string | 是 | 用户ID |
| userAttributes | Object | 否 | 用户属性 |

#### 返回值
- `any` - 实验变量值

#### 示例
```javascript
const buttonColor = DataTester.getExperimentVariableValue(
  'button_test',
  'color',
  '#1890ff',
  'user123'
);

const buttonText = DataTester.getExperimentVariableValue(
  'button_test',
  'text',
  '购买',
  'user123'
);
```

### DataTester.getExperimentConfig()
获取完整的实验配置

#### 语法
```javascript
DataTester.getExperimentConfig(experimentKey, defaultConfig, userId, userAttributes)
```

#### 参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| experimentKey | string | 是 | 实验键名 |
| defaultConfig | Object | 是 | 默认配置对象 |
| userId | string | 是 | 用户ID |
| userAttributes | Object | 否 | 用户属性 |

#### 返回值
- `Object` - 实验配置对象，包含variant_name字段

#### 示例
```javascript
const experimentConfig = DataTester.getExperimentConfig(
  'checkout_flow_test',
  {
    steps: 3,
    layout: 'vertical',
    show_progress: true
  },
  'user123'
);

console.log(experimentConfig);
// 输出: { 
//   steps: 2, 
//   layout: 'horizontal', 
//   show_progress: false,
//   variant_name: 'treatment_a'
// }
```

### DataTester.getAllExperiments()
获取用户参与的所有实验

#### 语法
```javascript
DataTester.getAllExperiments(userId, userAttributes)
```

#### 返回值
- `Array` - 实验信息数组

#### 示例
```javascript
const experiments = DataTester.getAllExperiments('user123');
console.log(experiments);
// 输出: [
//   { experiment_key: 'button_test', variant_name: 'treatment' },
//   { experiment_key: 'layout_test', variant_name: 'control' }
// ]
```

## 事件追踪 API

### DataTester.trackEvent()
追踪自定义事件

#### 语法
```javascript
DataTester.trackEvent(eventName, properties, userId)
```

#### 参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| eventName | string | 是 | 事件名称 |
| properties | Object | 否 | 事件属性 |
| userId | string | 否 | 用户ID |

#### 示例
```javascript
// 追踪点击事件
DataTester.trackEvent('button_click', {
  button_id: 'purchase_btn',
  page: 'product_detail',
  experiment: 'button_color_test'
}, 'user123');

// 追踪转化事件
DataTester.trackEvent('purchase_completed', {
  order_id: 'order_12345',
  amount: 99.99,
  currency: 'CNY'
}, 'user123');
```

### DataTester.trackExposure()
追踪实验曝光

#### 语法
```javascript
DataTester.trackExposure(experimentKey, userId, properties)
```

#### 参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| experimentKey | string | 是 | 实验键名 |
| userId | string | 是 | 用户ID |
| properties | Object | 否 | 额外属性 |

#### 示例
```javascript
DataTester.trackExposure('button_color_test', 'user123', {
  page: 'homepage',
  timestamp: Date.now()
});
```

### DataTester.setUserProperty()
设置用户属性

#### 语法
```javascript
DataTester.setUserProperty(userId, properties)
```

#### 参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| userId | string | 是 | 用户ID |
| properties | Object | 是 | 用户属性对象 |

#### 示例
```javascript
DataTester.setUserProperty('user123', {
  user_level: 'premium',
  registration_date: '2024-01-01',
  city: 'Beijing',
  age: 25
});
```

## 用户管理 API

### DataTester.setUserId()
设置当前用户ID

#### 语法
```javascript
DataTester.setUserId(userId)
```

#### 参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| userId | string | 是 | 用户ID |

#### 示例
```javascript
DataTester.setUserId('user123');
```

### DataTester.getCurrentUserId()
获取当前用户ID

#### 语法
```javascript
DataTester.getCurrentUserId()
```

#### 返回值
- `string` - 当前用户ID

### DataTester.clearUser()
清除当前用户信息

#### 语法
```javascript
DataTester.clearUser()
```

## 配置管理 API

### DataTester.setLogLevel()
设置日志级别

#### 语法
```javascript
DataTester.setLogLevel(level)
```

#### 参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| level | string | 是 | 日志级别：'DEBUG', 'INFO', 'WARN', 'ERROR' |

#### 示例
```javascript
DataTester.setLogLevel('DEBUG');
```

### DataTester.clearCache()
清除本地缓存

#### 语法
```javascript
DataTester.clearCache()
```

### DataTester.forceVariant()
强制指定实验分组（仅用于测试）

#### 语法
```javascript
DataTester.forceVariant(experimentKey, variantName, userId)
```

#### 参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| experimentKey | string | 是 | 实验键名 |
| variantName | string | 是 | 分组名称 |
| userId | string | 是 | 用户ID |

#### 示例
```javascript
// 强制用户进入treatment组
DataTester.forceVariant('button_test', 'treatment', 'user123');
```

### DataTester.getDebugInfo()
获取调试信息

#### 语法
```javascript
DataTester.getDebugInfo(userId)
```

#### 返回值
- `Object` - 包含用户实验分配等调试信息

#### 示例
```javascript
const debugInfo = DataTester.getDebugInfo('user123');
console.log(debugInfo);
// 输出: {
//   user_id: 'user123',
//   experiments: [...],
//   feature_flags: {...},
//   sdk_version: '2.0.0'
// }
```

## 错误处理

所有API调用都应该包含适当的错误处理：

```javascript
try {
  const value = DataTester.getFeatureFlag('feature_key', defaultValue, userId);
  // 使用value
} catch (error) {
  console.error('获取Feature Flag失败:', error);
  // 使用默认值
  const value = defaultValue;
}
```

## 异步API

某些API支持异步调用：

```javascript
// Promise方式
DataTester.getFeatureFlagAsync('feature_key', defaultValue, userId)
  .then(value => {
    // 使用value
  })
  .catch(error => {
    console.error('获取失败:', error);
  });

// async/await方式
async function getConfig() {
  try {
    const value = await DataTester.getFeatureFlagAsync('feature_key', defaultValue, userId);
    return value;
  } catch (error) {
    console.error('获取失败:', error);
    return defaultValue;
  }
}
```

## 回调函数

设置配置更新回调：

```javascript
DataTester.onConfigUpdate((updatedConfigs) => {
  console.log('配置已更新:', updatedConfigs);
  // 重新应用配置
  applyNewConfigs(updatedConfigs);
});
```
